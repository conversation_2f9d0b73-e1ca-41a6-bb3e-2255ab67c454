#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断SOC曲线拟合问题
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import interpolate
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 重新导入修改后的模块
import importlib
import data_driven_ocv_optimizer
importlib.reload(data_driven_ocv_optimizer)

from data_driven_ocv_optimizer import DataDrivenOCVOptimizer

def diagnose_fitting_issues():
    """诊断SOC曲线拟合问题"""
    print('🔍 诊断SOC曲线拟合问题')
    print('='*60)

    # 加载数据
    data = pd.read_hdf('database/127_1_1_2023-12-25.h5')

    # 查找列名
    current_col = None
    voltage_col = None  
    soc_col = None

    for col in data.columns:
        if '2023-12-25_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif '2023-12-25_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
        elif '2023-12-25_1#BMS-堆内-1簇组SOC' in col:
            soc_col = col

    # 创建清理后的数据
    clean_data = pd.DataFrame({
        '电流': data[current_col],
        '电压': data[voltage_col],
        'SOC': data[soc_col]
    }, index=data.index)

    clean_data = clean_data.dropna()
    
    # 使用部分数据进行快速测试
    test_data = clean_data.sample(n=5000, random_state=42)
    
    print(f'📊 数据概况:')
    print(f'  测试数据点数: {len(test_data)}')
    print(f'  电压范围: {test_data["电压"].min():.3f}V ~ {test_data["电压"].max():.3f}V')
    print(f'  SOC范围: {test_data["SOC"].min():.1f}% ~ {test_data["SOC"].max():.1f}%')
    print(f'  电流范围: {test_data["电流"].min():.1f}A ~ {test_data["电流"].max():.1f}A')

    # 创建优化器
    print(f'\n🔧 创建优化器进行快速测试...')
    optimizer = DataDrivenOCVOptimizer(
        data=test_data,
        current_col='电流',
        voltage_col='电压',
        soc_col='SOC',
        use_cuda=False
    )
    
    # 修改参数进行快速测试
    optimizer.POPULATION_SIZE = 100
    optimizer.GENERATIONS = 50
    
    print(f'  SOC采样点: {len(optimizer.soc_points)}')
    print(f'  SOC范围: {optimizer.soc_points[0]:.1f}% ~ {optimizer.soc_points[-1]:.1f}%')
    print(f'  OCV边界: {optimizer.ocv_bounds[0]:.3f}V ~ {optimizer.ocv_bounds[1]:.3f}V')

    # 运行快速优化
    print(f'\n⚡ 运行快速优化测试...')
    try:
        best_params, best_mae, history = optimizer.optimize_parameters()
        
        print(f'\n📈 优化结果:')
        print(f'  最佳MAE: {best_mae:.6f}V')
        print(f'  收敛代数: {len(history)}')
        
        # 分析拟合质量
        analyze_fitting_quality(optimizer, test_data, best_params)
        
        # 提供改进建议
        provide_improvement_suggestions(best_mae, history, optimizer)
        
    except Exception as e:
        print(f'❌ 优化过程出错: {e}')
        print('建议检查数据质量和参数设置')

def analyze_fitting_quality(optimizer, data, best_params):
    """分析拟合质量"""
    print(f'\n🔬 拟合质量分析:')
    
    # 提取参数
    n_soc = len(optimizer.soc_points)
    ocv_values = best_params[:n_soc]
    r0_values = best_params[n_soc:]
    
    # 检查OCV曲线单调性
    ocv_diff = np.diff(ocv_values)
    monotonic = np.all(ocv_diff >= 0)
    print(f'  OCV单调性: {"✅ 单调递增" if monotonic else "❌ 非单调"}')
    
    if not monotonic:
        negative_count = np.sum(ocv_diff < 0)
        print(f'    非单调点数: {negative_count}/{len(ocv_diff)}')
    
    # 检查参数范围
    ocv_range = np.max(ocv_values) - np.min(ocv_values)
    r0_range = np.max(r0_values) - np.min(r0_values)
    print(f'  OCV变化范围: {ocv_range:.3f}V')
    print(f'  R0变化范围: {r0_range:.6f}Ω')
    
    # 计算拟合误差分布
    current_values = data['电流'].values
    voltage_actual = data['电压'].values
    soc_values = data['SOC'].values
    
    # 插值计算预测电压
    ocv_interp = interpolate.interp1d(optimizer.soc_points, ocv_values, 
                                     kind='linear', fill_value='extrapolate')
    r0_interp = interpolate.interp1d(optimizer.soc_points, r0_values, 
                                    kind='linear', fill_value='extrapolate')
    
    ocv_pred = ocv_interp(soc_values)
    r0_pred = r0_interp(soc_values)
    voltage_pred = ocv_pred - current_values * r0_pred
    
    errors = np.abs(voltage_actual - voltage_pred)
    
    print(f'  误差统计:')
    print(f'    平均误差: {np.mean(errors):.6f}V')
    print(f'    最大误差: {np.max(errors):.6f}V')
    print(f'    误差标准差: {np.std(errors):.6f}V')
    print(f'    95%分位数: {np.percentile(errors, 95):.6f}V')

def provide_improvement_suggestions(best_mae, history, optimizer):
    """提供改进建议"""
    print(f'\n💡 改进建议:')
    
    # 基于MAE水平给出建议
    if best_mae > 0.01:
        print(f'  ⚠️  MAE较高 ({best_mae:.6f}V > 0.01V)')
        print(f'     建议1: 增加SOC采样点数量')
        print(f'     建议2: 调整适应度函数权重')
        print(f'     建议3: 检查数据质量和噪声')
    elif best_mae > 0.005:
        print(f'  🔶 MAE中等 ({best_mae:.6f}V)')
        print(f'     建议1: 微调遗传算法参数')
        print(f'     建议2: 增加约束条件')
    else:
        print(f'  ✅ MAE较低 ({best_mae:.6f}V < 0.005V)')
        print(f'     拟合质量良好')
    
    # 基于收敛情况给出建议
    if len(history) >= optimizer.GENERATIONS * 0.9:
        print(f'  ⚠️  可能未完全收敛')
        print(f'     建议: 增加GENERATIONS或调整收敛判据')
    
    # 具体参数调整建议
    print(f'\n🔧 具体参数调整建议:')
    print(f'  1. 增加SOC采样点:')
    print(f'     n_soc_points = 30-50 (当前: {len(optimizer.soc_points)})')
    
    print(f'  2. 优化遗传算法参数:')
    print(f'     POPULATION_SIZE = 300-800')
    print(f'     GENERATIONS = 1000-3000')
    print(f'     MUTATION_RATE = 0.15-0.25')
    
    print(f'  3. 改进适应度函数:')
    print(f'     - 加入平滑性约束')
    print(f'     - 使用加权误差')
    print(f'     - 考虑RMSE而非MAE')
    
    print(f'  4. 数据预处理:')
    print(f'     - 数据平滑滤波')
    print(f'     - 异常值检测和处理')
    print(f'     - 增加数据量')

if __name__ == "__main__":
    diagnose_fitting_issues()
