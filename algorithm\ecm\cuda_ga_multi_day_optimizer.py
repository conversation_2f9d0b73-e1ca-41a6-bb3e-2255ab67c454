#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CUDA加速的多天OCV-SOC曲线生成器
结合GPU并行计算和多天数据分析

🚀 直接运行说明:
1. 在IDE中直接点击运行即可
2. 如需修改配置，请编辑main()函数中的参数:
   - USE_CUDA: True启用GPU加速，False使用CPU
   - OUTPUT_DIR: 输出目录名称
3. 结果将保存在指定的输出目录中

📁 输出文件结构:
- json_data/: JSON格式的OCV-SOC数据
- plots/: 可视化图表
- logs/: 运行日志
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime
import json
from scipy.interpolate import interp1d
import time
import logging

try:
    import cupy as cp
    import cupyx.scipy.interpolate as cp_interp
    CUDA_AVAILABLE = True
    print("CUDA支持可用")
except ImportError:
    print("CuPy未安装，将使用CPU版本")
    print("安装命令: pip install cupy-cuda11x  # 根据CUDA版本选择")
    import numpy as cp
    CUDA_AVAILABLE = False

# 导入现有的GA优化器作为基础
from GA_battery_optimizer_DEAP_compatible import BatteryParameterOptimizer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CudaOptimizerConfig:
    """配置类，统一管理所有超参数"""
    # 电池模型参数
    CAPACITY_AH = 280  # 电芯容量(Ah)
    TARGET_CELL = 1    # 目标电芯编号
    
    # SOC点
    DEFAULT_SOC_POINTS = np.array([0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100])
    
    # R0相关参数
    R0_SOC_POINTS = np.array([0.0, 25.0, 50.0, 75.0, 100.0])  # 在关键SOC点定义内阻
    DEFAULT_R0 = np.array([0.0008, 0.0005, 0.0002, 0.0005, 0.0008])  # 随SOC变化的内阻值
    R0_BOUNDS_OFFSET = 0.0003  # R0边界偏移量
    
    # GA参数
    POPULATION_SIZE = 300
    GENERATIONS = 8000  # 修正为原来的值
    MUTATION_RATE = 0.15
    CROSSOVER_RATE = 0.8
    ELITE_RATIO = 0.1
    
    # GPU优化参数
    GPU_POPULATION_SIZE = 1000  # GPU模式下使用的种群大小
    GPU_GENERATIONS = 8000      # GPU模式下使用的迭代次数
    
    # 训练数据日期
    TRAINING_DATES = [
        "2023-12-25", "2023-12-26", "2023-12-27",
        "2023-12-28", "2023-12-29"
    ]
    
    # 数据库路径
    DATABASE_PATH = "database"
    
    # 输出目录
    DEFAULT_OUTPUT_DIR = "OCV_Results"

class CudaGAOptimizer:
    """CUDA加速的GA优化器"""
    
    def __init__(self, data, current_col, voltage_col, use_cuda=True):
        """
        初始化CUDA GA优化器
        
        Args:
            data: 训练数据
            current_col: 电流列名
            voltage_col: 电压列名
            use_cuda: 是否使用CUDA加速
        """
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col
        self.use_cuda = use_cuda and CUDA_AVAILABLE
        
        # 初始化基础优化器获取参数
        self.base_optimizer = BatteryParameterOptimizer(
            data=data, current_col=current_col, voltage_col=voltage_col
        )
        
        # 检查DEAP类是否已存在，避免重复创建
        try:
            from deap import creator
            if hasattr(creator, 'FitnessMin') and hasattr(creator, 'Individual'):
                print("DEAP类已存在，跳过初始化")
            else:
                print("初始化DEAP类")
        except ImportError:
            print("DEAP未安装")
        
        # 获取SOC点
        try:
            self.soc_points = self.base_optimizer.soc_points
        except AttributeError:
            # 如果base_optimizer没有soc_points，使用默认值
            self.soc_points = CudaOptimizerConfig.DEFAULT_SOC_POINTS
            
        # GA参数
        self.population_size = CudaOptimizerConfig.POPULATION_SIZE
        self.generations = CudaOptimizerConfig.GENERATIONS
        self.mutation_rate = CudaOptimizerConfig.MUTATION_RATE
        self.crossover_rate = CudaOptimizerConfig.CROSSOVER_RATE
        self.elite_ratio = CudaOptimizerConfig.ELITE_RATIO
        
        # 电池模型参数
        self.capacity_Ah = CudaOptimizerConfig.CAPACITY_AH  # 与基础优化器保持一致
        # 定义R0 SOC点 (用于可变内阻插值)
        self.r0_soc_points = CudaOptimizerConfig.R0_SOC_POINTS  # 在关键SOC点定义内阻
        # 默认R0值 (Ω)
        self.default_r0 = CudaOptimizerConfig.DEFAULT_R0  # 随SOC变化的内阻值
        self.r0_bounds = (self.default_r0 - CudaOptimizerConfig.R0_BOUNDS_OFFSET,
                         self.default_r0 + CudaOptimizerConfig.R0_BOUNDS_OFFSET)
        
        # GPU优化参数（当使用CUDA时）
        self.gpu_population_size = CudaOptimizerConfig.GPU_POPULATION_SIZE  # GPU模式下使用的种群大小
        self.gpu_generations = CudaOptimizerConfig.GPU_GENERATIONS      # GPU模式下使用的迭代次数
        
        if self.use_cuda:
            device = cp.cuda.Device()
            print(f"使用CUDA加速 (GPU ID: {device.id}, 内存: {device.mem_info[1] / 1024**3:.1f}GB)")
            print(f"CuPy版本: {cp.__version__}")
            # 将数据转移到GPU
            self._transfer_data_to_gpu()
            print("数据已成功转移到GPU，所有计算将在GPU上进行")
        else:
            print("🔄 使用CPU计算")
        
    def _transfer_data_to_gpu(self):
        """将数据转移到GPU"""
        if not self.use_cuda:
            return
            
        print("转移数据到GPU...")
        
        # 转移时间索引 - 转换为数值类型
        time_values = self.data.index.values
        # 将datetime转换为数值（纳秒）
        time_numeric = time_values.astype('datetime64[ns]').astype('int64')
        self.gpu_time_values = cp.asarray(time_numeric)
        
        # 转移电流和电压数据
        self.gpu_current = cp.asarray(self.data[self.current_col].values)
        self.gpu_voltage = cp.asarray(self.data[self.voltage_col].values)
        
        # 预计算时间差（纳秒转小时）
        time_diff = cp.diff(self.gpu_time_values)
        self.gpu_delta_t = cp.zeros(len(self.data))
        # 纳秒转小时：除以 (1e9 * 3600)
        self.gpu_delta_t[1:] = time_diff / (1e9 * 3600)
        
        # 转移SOC点
        self.gpu_soc_points = cp.asarray(self.soc_points)
        
        # 转移R0 SOC点
        self.r0_soc_points = cp.asarray(self.r0_soc_points)
        
        print(f"数据转移完成: {len(self.data)} 条记录")
        
    def _check_cuda_available(self):
        """检查CUDA是否可用"""
        if not self.use_cuda:
            raise RuntimeError("CUDA不可用，无法进行GPU加速计算")
    
    def _extract_params(self, gpu_params, single=False):
        """从GPU参数中提取初始SOC、OCV点和R0点
        
        Args:
            gpu_params: GPU参数数组
            single: 是否为单个参数（True）还是批量参数（False）
            
        Returns:
            initial_soc(s): 初始SOC值或数组
            ocv_points(s): OCV点数组或批量数组
            r0_points(s): R0点数组或批量数组
        """
        if single:
            initial_soc = gpu_params[0]
            ocv_points = gpu_params[1:len(self.soc_points)+1]
            r0_points = gpu_params[len(self.soc_points)+1:]
        else:
            initial_soc = gpu_params[:, 0]  # [batch_size]
            ocv_points = gpu_params[:, 1:len(self.soc_points)+1]  # [batch_size, n_soc_points]
            r0_points = gpu_params[:, len(self.soc_points)+1:]  # [batch_size, n_r0_points]
        
        return initial_soc, ocv_points, r0_points
    
    def _is_monotonic_increasing(self, values):
        """检查数组是否单调递增"""
        if len(values) <= 1:
            return True
        for i in range(1, len(values)):
            if values[i] < values[i-1]:
                return False
        return True
    
    def cuda_simulate_battery(self, params_batch):
        """
        CUDA加速的批量电池模拟
        
        Args:
            params_batch: 参数批次 [batch_size, param_count]
            
        Returns:
            fitness_batch: 适应度值 [batch_size]
        """
        self._check_cuda_available()
        
        batch_size = params_batch.shape[0]
        
        # 转移参数到GPU
        gpu_params = cp.asarray(params_batch)
        
        # 提取初始SOC、OCV点和R0点
        initial_socs, ocv_points_batch, r0_points_batch = self._extract_params(gpu_params)
        
        # 批量插值计算OCV
        fitness_values = cp.zeros(batch_size)
        
        for i in range(batch_size):
            try:
                # 单个样本的计算
                initial_soc = initial_socs[i]
                ocv_points = ocv_points_batch[i]
                r0_points = r0_points_batch[i]
                
                # 计算SOC变化
                delta_soc = self.gpu_current * self.gpu_delta_t / self.capacity_Ah * 100
                soc = initial_soc - cp.cumsum(delta_soc)
                soc = cp.clip(soc, 0, 100)
                
                # 插值计算OCV
                ocv = cp.interp(soc, self.gpu_soc_points, ocv_points)
                
                # 插值计算R0
                r0 = cp.interp(soc, self.r0_soc_points, r0_points)
                
                # 计算端电压
                voltage_sim = ocv - self.gpu_current * r0
                
                # 计算误差
                errors = voltage_sim - self.gpu_voltage
                mae = cp.mean(cp.abs(errors))
                
                fitness_values[i] = mae
                
                # 添加OCV单调性约束检查
                # 如果OCV不是单调递增的，增加惩罚
                if not self._is_monotonic_increasing(ocv_points):
                    fitness_values[i] += 0.1  # 添加固定惩罚
                
            except Exception as e:
                # 如果GPU计算失败，抛出异常而不是设置高惩罚值
                print(f"GPU计算失败 (个体 {i}): {e}")
                raise RuntimeError(f"GPU计算失败: {e}") from e
        
        # 转回CPU
        return cp.asnumpy(fitness_values)
        
    def get_detailed_simulation_results(self, params):
        """
        获取详细的模拟结果（GPU版本）
        
        Args:
            params: 参数数组
            
        Returns:
            sim_results: 包含详细模拟结果的字典
        """
        self._check_cuda_available()
        
        # 转移参数到GPU
        gpu_params = cp.asarray(params)
        
        # 提取初始SOC、OCV点和R0点
        initial_soc, ocv_points, r0_points = self._extract_params(gpu_params, single=True)
        
        # 计算SOC变化
        delta_soc = self.gpu_current * self.gpu_delta_t / self.capacity_Ah * 100
        soc = initial_soc - cp.cumsum(delta_soc)
        soc = cp.clip(soc, 0, 100)
        
        # 插值计算OCV
        ocv = cp.interp(soc, self.gpu_soc_points, ocv_points)
        
        # 插值计算R0
        r0 = cp.interp(soc, self.r0_soc_points, r0_points)
        
        # 计算端电压
        voltage_sim = ocv - self.gpu_current * r0
        
        # 计算误差
        errors = voltage_sim - self.gpu_voltage
        mae = cp.mean(cp.abs(errors))
        mse = cp.mean(errors**2)
        rmse = cp.sqrt(mse)
        max_error = cp.max(cp.abs(errors))
        
        # 转回CPU并返回结果
        return {
            'voltage': cp.asnumpy(voltage_sim),
            'soc': cp.asnumpy(soc),
            'ocv': cp.asnumpy(ocv),
            'r0': cp.asnumpy(r0),
            'errors': cp.asnumpy(errors),
            'mae': float(mae),
            'mse': float(mse),
            'rmse': float(rmse),
            'max_error': float(max_error)
        }
        
    def cuda_optimize_population(self):
        """CUDA加速的种群优化"""
        print(f"\n开始CUDA加速GA优化...")
        print(f"  种群大小: {self.population_size}")
        print(f"  迭代次数: {self.generations}")
        print(f"  使用设备: {'GPU' if self.use_cuda else 'CPU'}")
        
        start_time = time.time()
        
        # 初始化种群
        population = self._initialize_population()
        best_fitness_history = []
        
        for generation in range(self.generations):
            gen_start = time.time()
            
            # 批量评估适应度
            batch_size = min(50, self.population_size)  # 控制GPU内存使用
            fitness_values = []
            
            for i in range(0, self.population_size, batch_size):
                batch_end = min(i + batch_size, self.population_size)
                batch_params = np.array(population[i:batch_end])
                
                batch_fitness = self.cuda_simulate_battery(batch_params)
                fitness_values.extend(batch_fitness)
            
            fitness_values = np.array(fitness_values)
            
            # 计算种群多样性 (标准差)
            pop_array = np.array(population)
            diversity = np.mean(np.std(pop_array, axis=0))
            
            # 动态调整参数
            self._adjust_parameters(generation, diversity)
            
            # 选择、交叉、变异
            population = self._evolve_population(population, fitness_values)
            
            # 记录最佳适应度
            best_fitness = np.min(fitness_values)
            best_fitness_history.append(best_fitness)
            
            gen_time = time.time() - gen_start
            
            # 早停检测 (连续50代改进<0.1%)
            if generation > 200:
                last_50 = best_fitness_history[-50:]
                improvement = (last_50[0] - last_50[-1]) / last_50[0]
                if improvement < 0.0005:
                    print(f"🛑 早停触发: 第{generation}代")
                    break
            
            if generation % 100 == 0 or generation == self.generations - 1:
                print(f"  第 {generation:4d} 代: 最佳MAE={best_fitness:.6f}V, "
                      f"多样性={diversity:.4f}, "
                      f"变异率={self.mutation_rate:.3f}, "
                      f"交叉率={self.crossover_rate:.3f}, "
                      f"用时={gen_time:.2f}s")
        
        # 找到最优解
        final_fitness = self.cuda_simulate_battery(np.array(population))
        best_idx = np.argmin(final_fitness)
        best_params = population[best_idx]
        best_mae = final_fitness[best_idx]
        
        total_time = time.time() - start_time
        
        print(f"\nCUDA GA优化完成!")
        print(f"  最佳MAE: {best_mae:.6f}V")
        print(f"  总用时: {total_time:.1f}秒")
        print(f"  实际迭代: {len(best_fitness_history)}代")
        print(f"  平均每代: {total_time/max(1, len(best_fitness_history)):.3f}秒")
        
        return best_params, best_mae, best_fitness_history

    def _adjust_parameters(self, generation, diversity):
        """动态调整遗传算法参数"""
        # 随着迭代增加，减小变异率
        self.mutation_rate = max(0.01, 0.1 * (1 - generation/self.generations))
        
        # 根据种群多样性调整交叉率
        if diversity < 0.05:  # 低多样性
            self.crossover_rate = min(0.95, self.crossover_rate + 0.05)
        else:  # 高多样性
            self.crossover_rate = max(0.6, self.crossover_rate - 0.03)
        
    def _initialize_population(self):
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            individual = []
            
            # 初始SOC (4-6%)
            individual.append(np.random.uniform(4, 6))
            
            # OCV点 (2.8-3.6V) - 确保单调递增
            ocv_points = []
            # 第一个OCV点
            ocv_points.append(np.random.uniform(2.8, 3.0))
            # 后续OCV点，确保比前一个点大
            for i in range(1, len(self.soc_points)):
                min_ocv = ocv_points[-1] + 0.001  # 比前一个点至少大0.001V
                max_ocv = max(min_ocv + 0.1, 3.6)  # 确保不超过3.6V
                ocv_points.append(np.random.uniform(min_ocv, max_ocv))
            
            individual.extend(ocv_points)
                
            # R0点 (0.0001-0.0015Ω)
            for _ in range(len(self.r0_soc_points)):
                individual.append(np.random.uniform(0.0001, 0.0015))
                
            population.append(individual)
            
        return population
        
    def _evolve_population(self, population, fitness_values):
        """进化种群"""
        # 精英选择
        elite_count = int(self.population_size * self.elite_ratio)
        elite_indices = np.argsort(fitness_values)[:elite_count]
        new_population = [population[i] for i in elite_indices]
        
        # 生成剩余个体
        while len(new_population) < self.population_size:
            # 锦标赛选择
            parent1 = self._tournament_selection(population, fitness_values)
            parent2 = self._tournament_selection(population, fitness_values)
            
            # 交叉
            if np.random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            child1 = self._mutate(child1)
            child2 = self._mutate(child2)
            
            new_population.extend([child1, child2])
        
        return new_population[:self.population_size]
        
    def _tournament_selection(self, population, fitness_values, tournament_size=3):
        """锦标赛选择"""
        indices = np.random.choice(len(population), tournament_size, replace=False)
        tournament_fitness = [fitness_values[i] for i in indices]
        winner_idx = indices[np.argmin(tournament_fitness)]
        return population[winner_idx].copy()
        
    def _crossover(self, parent1, parent2):
        """单点交叉"""
        crossover_point = np.random.randint(1, len(parent1))
        
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        
        return child1, child2
        
    def _mutate(self, individual):
        """高斯变异"""
        mutated = individual.copy()
        
        for i in range(len(individual)):
            if np.random.random() < self.mutation_rate:
                if i == 0:  # 初始SOC
                    mutated[i] += np.random.normal(0, 0.5)
                    mutated[i] = np.clip(mutated[i], 4, 6)
                elif 1 <= i <= len(self.soc_points):  # OCV点
                    mutated[i] += np.random.normal(0, 0.01)
                    mutated[i] = np.clip(mutated[i], 2.8, 3.6)
                else:  # R0点
                    mutated[i] += np.random.normal(0, 0.0001)
                    mutated[i] = np.clip(mutated[i], 0.0001, 0.0015)
        
        # 确保OCV点单调递增
        ocv_start = 1
        ocv_end = len(self.soc_points) + 1
        ocv_points = mutated[ocv_start:ocv_end]
        
        # 如果OCV点不是单调递增的，进行修正
        if not self._is_monotonic_increasing(ocv_points):
            # 使用线性插值确保单调递增
            for j in range(1, len(ocv_points)):
                if ocv_points[j] < ocv_points[j-1]:
                    # 设置为前一个值加上一个小的增量
                    ocv_points[j] = ocv_points[j-1] + 0.001
            
            # 更新变异后的个体
            mutated[ocv_start:ocv_end] = ocv_points
                    
        return mutated


def test_cuda_ga():
    """测试CUDA GA优化器"""
    print("="*60)
    print("CUDA GA优化器测试")
    print("="*60)
    
    # 加载测试数据
    print("加载测试数据...")
    data = pd.read_hdf('database/127_1_1_2023-12-25.h5')
    
    # 提取列
    current_col = None
    voltage_col = None
    
    for col in data.columns:
        if '2023-12-25_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif '2023-12-25_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
    
    if not (current_col and voltage_col):
        raise ValueError("找不到必要的数据列")
    
    # 重命名数据
    test_data = pd.DataFrame({
        '1#BMS-堆内-1簇组电流': data[current_col],
        '1#BMS-堆内-1簇单体电压1': data[voltage_col]
    }, index=data.index)
    
    print(f"数据准备完成: {len(test_data)} 条记录")
    
    # 创建CUDA优化器
    cuda_optimizer = CudaGAOptimizer(
        data=test_data,
        current_col='1#BMS-堆内-1簇组电流',
        voltage_col='1#BMS-堆内-1簇单体电压1',
        use_cuda=True
    )
    
    # 运行优化
    best_params, best_mae, history = cuda_optimizer.cuda_optimize_population()
    
    print(f"\n优化结果:")
    print(f"  最佳MAE: {best_mae:.6f}V")
    print(f"  初始SOC: {best_params[0]:.2f}%")
    print(f"  OCV范围: {min(best_params[1:len(cuda_optimizer.soc_points)+1]):.3f}V - {max(best_params[1:len(cuda_optimizer.soc_points)+1]):.3f}V")
    if len(best_params) > len(cuda_optimizer.soc_points)+1:
        print(f"  R0范围: {min(best_params[len(cuda_optimizer.soc_points)+1:]):.6f}Ω - {max(best_params[len(cuda_optimizer.soc_points)+1:]):.6f}Ω")
    
    return best_params, best_mae


class CudaMultiDayOCVGenerator:
    """CUDA加速的多天OCV-SOC曲线生成器"""
    
    def __init__(self, use_cuda=True, output_dir=None):
        self.database_path = CudaOptimizerConfig.DATABASE_PATH
        self.training_dates = CudaOptimizerConfig.TRAINING_DATES
        self.daily_results = {}
        self.balanced_curve = None
        self.use_cuda = use_cuda and CUDA_AVAILABLE

        # 设置输出目录
        self.output_dir = output_dir if output_dir else CudaOptimizerConfig.DEFAULT_OUTPUT_DIR
        self._create_output_directories()
        
        if self.use_cuda:
            device = cp.cuda.Device()
            print(f"CUDA模式: GPU ID {device.id}, 内存: {device.mem_info[1] / 1024**3:.1f} GB")

    def _create_output_directories(self):
        """创建输出目录结构"""
        # 主输出目录
        os.makedirs(self.output_dir, exist_ok=True)

        # 子目录
        self.json_dir = os.path.join(self.output_dir, "json_data")
        self.plots_dir = os.path.join(self.output_dir, "plots")
        self.logs_dir = os.path.join(self.output_dir, "logs")

        os.makedirs(self.json_dir, exist_ok=True)
        os.makedirs(self.plots_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)

        print(f"📁 输出目录已创建:")
        print(f"   主目录: {self.output_dir}")
        print(f"   JSON数据: {self.json_dir}")
        print(f"   图表: {self.plots_dir}")
        print(f"   日志: {self.logs_dir}")

        # 设置日志
        self._setup_logging()
    
    def _get_timestamp(self):
        """获取时间戳"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def _setup_logging(self):
        """设置日志记录"""
        timestamp = self._get_timestamp()
        log_filename = f"cuda_ocv_generation_{timestamp}.log"
        log_filepath = os.path.join(self.logs_dir, log_filename)

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filepath, encoding='utf-8'),
                logging.StreamHandler()  # 同时输出到控制台
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志系统初始化完成，日志文件: {log_filepath}")

    def load_single_day_data(self, date):
        """加载单天数据"""
        file_path = os.path.join(self.database_path, f"127_1_1_{date}.h5")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        data = pd.read_hdf(file_path)
        
        # 查找需要的列
        current_col = None
        voltage_col = None
        soc_col = None
        
        for col in data.columns:
            if f'{date}_1#BMS-堆内-1簇组电流' == col:
                current_col = col
            elif f'{date}_1#BMS-堆内-1簇单体电压1' == col:
                voltage_col = col
            elif f'{date}_1#BMS-堆内-1簇组SOC' == col:
                soc_col = col
        
        if not (current_col and voltage_col and soc_col):
            raise ValueError(f"{date} 数据缺少必要列")
        
        # 重命名列
        day_data = pd.DataFrame({
            '1#BMS-堆内-1簇组电流': data[current_col],
            '1#BMS-堆内-1簇单体电压1': data[voltage_col],
            '1#BMS-堆内-1簇组SOC': data[soc_col]
        }, index=data.index)
        
        return day_data
        
    def cuda_optimize_single_day(self, date, data):
        """使用CUDA优化单天数据"""
        print(f"\n🚀 CUDA优化 {date} 数据...")
        print(f"  数据量: {len(data)} 条记录")
        print(f"  时间范围: {data.index[0]} 到 {data.index[-1]}")
        
        # 创建CUDA GA优化器
        cuda_optimizer = CudaGAOptimizer(
            data=data,
            current_col='1#BMS-堆内-1簇组电流',
            voltage_col='1#BMS-堆内-1簇单体电压1',
            use_cuda=self.use_cuda
        )
        
        # 调整GA参数以适应GPU内存
        if self.use_cuda:
            cuda_optimizer.population_size = cuda_optimizer.gpu_population_size
            cuda_optimizer.generations = cuda_optimizer.gpu_generations
        
        # 运行优化
        best_params, best_mae, history = cuda_optimizer.cuda_optimize_population()
        
        # 提取结果
        initial_soc = best_params[0]
        ocv_points = best_params[1:len(cuda_optimizer.soc_points)+1]
        soc_points = cuda_optimizer.soc_points  # 使用cuda_optimizer的soc_points
        
        # 获取详细模拟结果（使用GPU版本获取详细指标）
        if hasattr(cuda_optimizer, 'get_detailed_simulation_results'):
            # 如果有GPU版本的详细结果方法，使用它
            sim_results = cuda_optimizer.get_detailed_simulation_results(best_params)
        else:
            # 否则使用CPU版本（临时解决方案）
            cuda_optimizer.base_optimizer.return_detailed_metrics = True
            sim_results = cuda_optimizer.base_optimizer.simulate_battery(best_params)
            print("警告: 使用CPU版本获取详细模拟结果，这可能会影响性能")
        
        print(f"  ✅ 优化完成 - MAE: {best_mae:.6f}V, 初始SOC: {initial_soc:.2f}%")
        
        return {
            'date': date,
            'best_params': best_params,
            'best_mae': best_mae,
            'initial_soc': initial_soc,
            'soc_points': soc_points,
            'ocv_points': ocv_points,
            'r0_soc_points': cuda_optimizer.r0_soc_points if hasattr(cuda_optimizer, 'r0_soc_points') else None,
            'r0_points': best_params[len(soc_points)+1:] if len(best_params) > len(soc_points)+1 else None,
            'sim_results': sim_results,
            'data_count': len(data),
            'fitness_history': history
        }
        
    def _ensure_numpy_array(self, arr):
        """确保输入是NumPy数组"""
        return arr.get() if hasattr(arr, 'get') else np.array(arr)
    
    def generate_balanced_curve(self):
        """生成均衡的OCV-SOC曲线"""
        print(f"\n📊 生成均衡OCV-SOC曲线...")
        
        if not self.daily_results:
            raise ValueError("没有日常结果数据")
        
        # 收集所有日常曲线数据
        all_soc_points = []
        all_ocv_curves = []
        all_r0_soc_points = []
        all_r0_curves = []
        weights = []
        
        for date, result in self.daily_results.items():
            soc_points = result['soc_points']
            ocv_points = result['ocv_points']
            mae = result['best_mae']
            data_count = result['data_count']
            
            # 权重基于MAE的倒数和数据量
            weight = (1.0 / mae) * np.sqrt(data_count / 50000)
            weights.append(weight)
            
            all_soc_points.append(soc_points)
            all_ocv_curves.append(ocv_points)
            
            # 如果有R0参数，也收集
            if result.get('r0_soc_points') is not None and result.get('r0_points') is not None:
                all_r0_soc_points.append(result['r0_soc_points'])
                all_r0_curves.append(result['r0_points'])
            
            print(f"  {date}: MAE={mae:.6f}V, 权权={weight:.3f}")
        
        # 创建统一的SOC网格
        unified_soc = np.arange(0, 101, 1)
        
        # 将每条OCV曲线插值到统一网格
        interpolated_ocv_curves = []
        for i, (soc_pts, ocv_pts) in enumerate(zip(all_soc_points, all_ocv_curves)):
            # 确保使用NumPy数组进行插值
            soc_pts_np = self._ensure_numpy_array(soc_pts)
            ocv_pts_np = self._ensure_numpy_array(ocv_pts)
            unified_soc_np = self._ensure_numpy_array(unified_soc)
            
            interp_func = interp1d(soc_pts_np, ocv_pts_np, kind='linear',
                                 fill_value='extrapolate')
            interpolated_ocv = interp_func(unified_soc_np)
            interpolated_ocv_curves.append(interpolated_ocv)
        
        # 加权平均计算均衡OCV曲线
        weights = np.array(weights) / np.sum(weights)
        
        # 确保使用NumPy数组
        unified_soc_np = self._ensure_numpy_array(unified_soc)
        balanced_ocv = np.zeros_like(unified_soc_np, dtype=float)
        for i, curve in enumerate(interpolated_ocv_curves):
            curve_np = self._ensure_numpy_array(curve)
            balanced_ocv += weights[i] * curve_np
        
        # 计算OCV统计信息
        # 确保所有曲线都是NumPy数组
        interpolated_ocv_curves_np = []
        for curve in interpolated_ocv_curves:
            curve_np = self._ensure_numpy_array(curve)
            interpolated_ocv_curves_np.append(curve_np)
        
        ocv_curve_std = np.std(interpolated_ocv_curves_np, axis=0)
        ocv_curve_range = np.max(interpolated_ocv_curves_np, axis=0) - np.min(interpolated_ocv_curves_np, axis=0)
        
        # 处理R0曲线（如果存在）
        balanced_r0 = None
        r0_curve_std = None
        r0_curve_range = None
        interpolated_r0_curves = []
        
        if all_r0_soc_points and all_r0_curves:
            # 创建统一的R0 SOC网格（使用第一个的SOC点）
            if len(all_r0_soc_points) > 0:
                unified_r0_soc = self._ensure_numpy_array(all_r0_soc_points[0])
                
                # 将每条R0曲线插值到统一网格
                for i, (r0_soc_pts, r0_pts) in enumerate(zip(all_r0_soc_points, all_r0_curves)):
                    # 确保使用NumPy数组进行插值
                    r0_soc_pts_np = self._ensure_numpy_array(r0_soc_pts)
                    r0_pts_np = self._ensure_numpy_array(r0_pts)
                    unified_r0_soc_np = self._ensure_numpy_array(unified_r0_soc)
                    
                    interp_func = interp1d(r0_soc_pts_np, r0_pts_np, kind='linear',
                                         fill_value='extrapolate')
                    interpolated_r0 = interp_func(unified_r0_soc_np)
                    interpolated_r0_curves.append(interpolated_r0)
                
                # 加权平均计算均衡R0曲线
                unified_r0_soc_np = self._ensure_numpy_array(unified_r0_soc)
                balanced_r0 = np.zeros_like(unified_r0_soc_np, dtype=float)
                for i, curve in enumerate(interpolated_r0_curves):
                    curve_np = self._ensure_numpy_array(curve)
                    balanced_r0 += weights[i] * curve_np
                
                # 计算R0统计信息
                # 确保所有曲线都是NumPy数组
                interpolated_r0_curves_np = []
                for curve in interpolated_r0_curves:
                    curve_np = self._ensure_numpy_array(curve)
                    interpolated_r0_curves_np.append(curve_np)
                
                r0_curve_std = np.std(interpolated_r0_curves_np, axis=0)
                r0_curve_range = np.max(interpolated_r0_curves_np, axis=0) - np.min(interpolated_r0_curves_np, axis=0)
        
        self.balanced_curve = {
            'soc_points': unified_soc,
            'ocv_values': balanced_ocv,
            'weights': weights,
            'individual_ocv_curves': interpolated_ocv_curves,
            'ocv_curve_std': ocv_curve_std,
            'ocv_curve_range': ocv_curve_range,
            'avg_mae': np.average([r['best_mae'] for r in self.daily_results.values()],
                                weights=weights)
        }
        
        # 如果有R0数据，也添加到均衡曲线中
        if balanced_r0 is not None:
            self.balanced_curve['r0_soc_points'] = unified_r0_soc
            self.balanced_curve['r0_values'] = balanced_r0
            self.balanced_curve['individual_r0_curves'] = interpolated_r0_curves
            self.balanced_curve['r0_curve_std'] = r0_curve_std
            self.balanced_curve['r0_curve_range'] = r0_curve_range
        
        print(f"  ✅ 均衡曲线生成完成")
        print(f"  加权平均MAE: {self.balanced_curve['avg_mae']:.6f}V")
        print(f"  OCV最大标准差: {np.max(ocv_curve_std):.6f}V")
        if r0_curve_std is not None:
            print(f"  R0最大标准差: {np.max(r0_curve_std):.6f}Ω")
        
        return self.balanced_curve
        
    def save_results(self):
        """保存结果"""
        timestamp = self._get_timestamp()
        
        # 构建结果数据
        result_data = {
            "generation_info": {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "cell_id": CudaOptimizerConfig.TARGET_CELL,
                "capacity_ah": CudaOptimizerConfig.CAPACITY_AH,
                "training_dates": self.training_dates,
                "method": "cuda_accelerated_multi_day_ga",
                "cuda_enabled": self.use_cuda
            },
            "daily_results": {},
            "balanced_curve": {
                "soc_points": self.balanced_curve['soc_points'].tolist(),
                "ocv_values": self.balanced_curve['ocv_values'].tolist(),
                "weights": self.balanced_curve['weights'].tolist(),
                "avg_mae": float(self.balanced_curve['avg_mae']),
                "ocv_max_std": float(np.max(self.balanced_curve['ocv_curve_std'])),
                "ocv_max_range": float(np.max(self.balanced_curve['ocv_curve_range']))
            }
        }
        
        # 如果有均衡的R0曲线，也添加到结果中
        if 'r0_values' in self.balanced_curve:
            result_data["balanced_curve"]["r0_soc_points"] = self.balanced_curve['r0_soc_points'].tolist()
            result_data["balanced_curve"]["r0_values"] = self.balanced_curve['r0_values'].tolist()
            result_data["balanced_curve"]["r0_max_std"] = float(np.max(self.balanced_curve['r0_curve_std']))
            result_data["balanced_curve"]["r0_max_range"] = float(np.max(self.balanced_curve['r0_curve_range']))
        
        # 添加每日结果
        for date, result in self.daily_results.items():
            daily_result = {
                "initial_soc": float(result['initial_soc']),
                "best_mae": float(result['best_mae']),
                "data_count": result['data_count'],
                "soc_points": result['soc_points'].tolist() if hasattr(result['soc_points'], 'tolist') else result['soc_points'],
                "ocv_points": result['ocv_points'].tolist() if hasattr(result['ocv_points'], 'tolist') else result['ocv_points'],
                "quality_metrics": {
                    "mae": float(result['sim_results']['mae']),
                    "rmse": float(result['sim_results']['rmse']),
                    "max_error": float(result['sim_results']['max_error'])
                }
            }
            
            # 如果有R0参数，也保存
            if result.get('r0_soc_points') is not None and result.get('r0_points') is not None:
                daily_result["r0_soc_points"] = result['r0_soc_points'].tolist() if hasattr(result['r0_soc_points'], 'tolist') else result['r0_soc_points']
                daily_result["r0_points"] = result['r0_points'].tolist() if hasattr(result['r0_points'], 'tolist') else result['r0_points']
                
            result_data["daily_results"][date] = daily_result
        
        # 保存JSON文件到指定目录
        json_filename = f"cuda_multi_day_ocv_curves_{timestamp}.json"
        json_filepath = os.path.join(self.json_dir, json_filename)

        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 结果已保存: {json_filepath}")
        return json_filepath
        
    def generate_visualization(self):
        """生成可视化图表 - 每个图表单独保存"""
        timestamp = self._get_timestamp()
        colors = ['blue', 'red', 'green', 'orange', 'purple']
        plot_files = []

        print("📊 生成可视化图表...")

        try:
            # 1. 各日OCV-SOC曲线对比图
            plt.figure(figsize=(12, 8))
            for i, (date, result) in enumerate(self.daily_results.items()):
                color = colors[i % len(colors)]
                plt.plot(result['soc_points'], result['ocv_points'],
                        color=color, alpha=0.8, linewidth=2,
                        label=f'{date} (MAE:{result["best_mae"]:.4f}V)')

            plt.xlabel('SOC (%)', fontsize=12)
            plt.ylabel('OCV (V)', fontsize=12)
            plt.title('各日OCV-SOC曲线对比', fontsize=14, fontweight='bold')
            plt.legend(fontsize=10)
            plt.grid(True, alpha=0.3)

            plot_filename = f"daily_ocv_comparison_{timestamp}.png"
            plot_filepath = os.path.join(self.plots_dir, plot_filename)
            plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_filepath)
            print(f"  ✅ 各日OCV对比图: {plot_filename}")
        except Exception as e:
            print(f"  ❌ 各日OCV对比图生成失败: {e}")

        try:
            # 2. 均衡OCV-SOC曲线图
            plt.figure(figsize=(12, 8))

            # 绘制个别曲线（浅色）
            for i, curve in enumerate(self.balanced_curve['individual_ocv_curves']):
                plt.plot(self.balanced_curve['soc_points'], curve,
                        color='gray', alpha=0.3, linewidth=1)

            # 绘制均衡曲线（突出显示）
            plt.plot(self.balanced_curve['soc_points'], self.balanced_curve['ocv_values'],
                    'r-', linewidth=3, label=f'均衡曲线 (MAE:{self.balanced_curve["avg_mae"]:.4f}V)')

            plt.xlabel('SOC (%)', fontsize=12)
            plt.ylabel('OCV (V)', fontsize=12)
            plt.title('均衡OCV-SOC曲线', fontsize=14, fontweight='bold')
            plt.legend(fontsize=10)
            plt.grid(True, alpha=0.3)

            plot_filename = f"balanced_ocv_curve_{timestamp}.png"
            plot_filepath = os.path.join(self.plots_dir, plot_filename)
            plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_filepath)
            print(f"  ✅ 均衡OCV曲线图: {plot_filename}")
        except Exception as e:
            print(f"  ❌ 均衡OCV曲线图生成失败: {e}")

        try:
            # 3. 均衡R0-SOC曲线图（如果存在R0数据）
            if 'r0_values' in self.balanced_curve and 'r0_soc_points' in self.balanced_curve:
                plt.figure(figsize=(12, 8))

                # 绘制个别R0曲线（浅色）
                if 'individual_r0_curves' in self.balanced_curve:
                    for i, curve in enumerate(self.balanced_curve['individual_r0_curves']):
                        plt.plot(self.balanced_curve['r0_soc_points'], curve,
                                color='gray', alpha=0.3, linewidth=1)

                # 绘制均衡R0曲线（突出显示）
                plt.plot(self.balanced_curve['r0_soc_points'], self.balanced_curve['r0_values'],
                        'g-', linewidth=3, label=f'均衡R0曲线')

                plt.xlabel('SOC (%)', fontsize=12)
                plt.ylabel('R0 (Ω)', fontsize=12)
                plt.title('均衡R0-SOC曲线', fontsize=14, fontweight='bold')
                plt.legend(fontsize=10)
                plt.grid(True, alpha=0.3)

                plot_filename = f"balanced_r0_curve_{timestamp}.png"
                plot_filepath = os.path.join(self.plots_dir, plot_filename)
                plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files.append(plot_filepath)
                print(f"  ✅ 均衡R0曲线图: {plot_filename}")
        except Exception as e:
            print(f"  ❌ 均衡R0曲线图生成失败: {e}")

        # 4. 为每一天生成原始vs拟合对比图和电压曲线图
        for date, result in self.daily_results.items():
            self._generate_daily_comparison_plots(date, result, timestamp, plot_files)

        print(f"📊 共生成 {len(plot_files)} 个图表文件")
        return plot_files

    def _generate_daily_comparison_plots(self, date, result, timestamp, plot_files):
        """为单天数据生成对比图和电压曲线图"""
        colors = ['blue', 'red', 'green', 'orange', 'purple']

        # 重新加载原始数据用于对比
        try:
            original_data = self.load_single_day_data(date)

            try:
                # 4. 原始OCV vs 拟合OCV对比图
                plt.figure(figsize=(12, 8))

                # 从原始数据中提取SOC和计算原始OCV（使用电压作为近似）
                original_soc = original_data['1#BMS-堆内-1簇组SOC'].values
                original_voltage = original_data['1#BMS-堆内-1簇单体电压1'].values

                # 创建SOC区间并计算每个区间的电压范围
                soc_bins = np.arange(0, 101, 2)  # 每2%一个区间
                soc_centers = soc_bins[:-1] + 1  # 区间中心点

                voltage_means = []
                voltage_mins = []
                voltage_maxs = []
                voltage_stds = []

                for i in range(len(soc_bins)-1):
                    # 找到在当前SOC区间内的数据点
                    mask = (original_soc >= soc_bins[i]) & (original_soc < soc_bins[i+1])
                    if np.sum(mask) > 0:
                        voltages_in_bin = original_voltage[mask]
                        voltage_means.append(np.mean(voltages_in_bin))
                        voltage_mins.append(np.min(voltages_in_bin))
                        voltage_maxs.append(np.max(voltages_in_bin))
                        voltage_stds.append(np.std(voltages_in_bin))
                    else:
                        voltage_means.append(np.nan)
                        voltage_mins.append(np.nan)
                        voltage_maxs.append(np.nan)
                        voltage_stds.append(np.nan)

                voltage_means = np.array(voltage_means)
                voltage_mins = np.array(voltage_mins)
                voltage_maxs = np.array(voltage_maxs)
                voltage_stds = np.array(voltage_stds)

                # 移除NaN值
                valid_mask = ~np.isnan(voltage_means)
                soc_centers_valid = soc_centers[valid_mask]
                voltage_means_valid = voltage_means[valid_mask]
                voltage_mins_valid = voltage_mins[valid_mask]
                voltage_maxs_valid = voltage_maxs[valid_mask]
                voltage_stds_valid = voltage_stds[valid_mask]

                # 绘制原始数据范围（使用填充区域显示最大最小值范围）
                plt.fill_between(soc_centers_valid, voltage_mins_valid, voltage_maxs_valid,
                               alpha=0.2, color='lightblue', label='原始数据范围(最大-最小)')

                # 绘制平均值线
                plt.plot(soc_centers_valid, voltage_means_valid, 'o-',
                        color='blue', alpha=0.6, markersize=3, linewidth=1,
                        label='原始数据平均值')

                # 绘制标准差范围
                plt.fill_between(soc_centers_valid,
                               voltage_means_valid - voltage_stds_valid,
                               voltage_means_valid + voltage_stds_valid,
                               alpha=0.3, color='cyan', label='±1标准差范围')

                # 绘制拟合的OCV曲线
                plt.plot(result['soc_points'], result['ocv_points'],
                        'r-', linewidth=3, label=f'拟合OCV曲线 (MAE:{result["best_mae"]:.4f}V)')

                plt.xlabel('SOC (%)', fontsize=12)
                plt.ylabel('电压/OCV (V)', fontsize=12)
                plt.title(f'{date} - 原始数据 vs 拟合OCV曲线对比', fontsize=14, fontweight='bold')
                plt.legend(fontsize=10)
                plt.grid(True, alpha=0.3)

                plot_filename = f"ocv_comparison_{date}_{timestamp}.png"
                plot_filepath = os.path.join(self.plots_dir, plot_filename)
                plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files.append(plot_filepath)
                print(f"  ✅ {date} OCV对比图: {plot_filename}")
            except Exception as e:
                print(f"  ❌ {date} OCV对比图生成失败: {e}")

            try:
                # 5. 电压曲线图（时间序列）
                plt.figure(figsize=(15, 8))

                # 时间轴
                time_hours = range(len(original_data))

                # 绘制原始电压
                plt.plot(time_hours, original_voltage, 'b-', alpha=0.7, linewidth=1,
                        label='实测电压')

                # 如果有模拟结果，绘制模拟电压
                if 'sim_results' in result and 'voltage' in result['sim_results']:
                    sim_voltage = result['sim_results']['voltage']
                    plt.plot(time_hours, sim_voltage, 'r-', alpha=0.8, linewidth=1.5,
                            label=f'模拟电压 (MAE:{result["best_mae"]:.4f}V)')

                plt.xlabel('时间 (小时)', fontsize=12)
                plt.ylabel('电压 (V)', fontsize=12)
                plt.title(f'{date} - 电压时间序列对比', fontsize=14, fontweight='bold')
                plt.legend(fontsize=10)
                plt.grid(True, alpha=0.3)

                plot_filename = f"voltage_timeseries_{date}_{timestamp}.png"
                plot_filepath = os.path.join(self.plots_dir, plot_filename)
                plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files.append(plot_filepath)
                print(f"  ✅ {date} 电压时序图: {plot_filename}")
            except Exception as e:
                print(f"  ❌ {date} 电压时序图生成失败: {e}")

        except Exception as e:
            print(f"  ❌ {date} 图表生成失败: {e}")
        
    def run_complete_analysis(self):
        """运行完整的CUDA加速分析"""
        print("="*60)
        print("CUDA加速多天OCV-SOC曲线分析")
        print("="*60)
        
        total_start_time = time.time()
        
        try:
            # 1. 分别优化每天的数据
            print(f"\n🚀 第一阶段: CUDA加速各日数据优化")
            print("-" * 40)
            
            for date in self.training_dates:
                try:
                    day_data = self.load_single_day_data(date)
                    result = self.cuda_optimize_single_day(date, day_data)
                    self.daily_results[date] = result
                    
                except Exception as e:
                    print(f"  ❌ {date} 优化失败: {e}")
                    continue
            
            if not self.daily_results:
                raise ValueError("没有成功优化的日常数据")
            
            print(f"\n✅ 成功优化 {len(self.daily_results)} 天数据")
            
            # 2. 生成均衡曲线
            print(f"\n📊 第二阶段: 生成均衡曲线")
            print("-" * 40)
            
            balanced_curve = self.generate_balanced_curve()
            
            # 3. 保存结果
            print(f"\n💾 第三阶段: 保存结果")
            print("-" * 40)
            
            json_file = self.save_results()
            plot_files = self.generate_visualization()
            
            total_time = time.time() - total_start_time
            
            # 4. 输出总结
            print(f"\n" + "="*60)
            print("🎉 CUDA加速多天OCV-SOC曲线分析完成!")
            print("="*60)
            print(f"🚀 计算模式: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")
            print(f"📅 分析日期: {', '.join(self.training_dates)}")
            print(f"✅ 成功天数: {len(self.daily_results)}")
            print(f"📊 均衡曲线质量:")
            print(f"   - 加权平均MAE: {balanced_curve['avg_mae']:.6f}V")
            print(f"   - OCV最大标准差: {np.max(balanced_curve['ocv_curve_std']):.6f}V")
            if 'r0_curve_std' in balanced_curve and balanced_curve['r0_curve_std'] is not None:
                print(f"   - R0最大标准差: {np.max(balanced_curve['r0_curve_std']):.6f}Ω")
            print(f"⏱️  总用时: {total_time:.1f}秒")
            print(f"📁 输出文件:")
            print(f"   - JSON数据: {json_file}")
            print(f"   - 可视化图表: {len(plot_files)} 个文件")
            for i, plot_file in enumerate(plot_files, 1):
                print(f"     {i}. {plot_file}")
            print("="*60)

            return {
                'success': True,
                'daily_results': self.daily_results,
                'balanced_curve': balanced_curve,
                'json_file': json_file,
                'plot_files': plot_files,
                'total_time': total_time,
                'cuda_used': self.use_cuda
            }
            
        except Exception as e:
            print(f"\n❌ 分析失败: {e}")
            return {'success': False, 'error': str(e)}


def main():
    """主函数 - 直接运行配置"""

    # ==================== 配置参数 ====================
    # 您可以直接修改以下参数:

    USE_CUDA = True  # True: 启用GPU加速 | False: 使用CPU计算
    OUTPUT_DIR = CudaOptimizerConfig.DEFAULT_OUTPUT_DIR  # 输出目录名称

    # ================================================

    print(f"🚀 运行模式: {'GPU (CUDA)' if USE_CUDA else 'CPU'}")
    print(f"📁 输出目录: {OUTPUT_DIR}")

    generator = CudaMultiDayOCVGenerator(
        use_cuda=USE_CUDA,
        output_dir=OUTPUT_DIR
    )
    result = generator.run_complete_analysis()

    if result['success']:
        print(f"\n🎉 多天OCV-SOC曲线分析成功!")
        if result.get('cuda_used', False):
            print(f"⚡ GPU加速效果显著!")
    else:
        print(f"\n💥 分析失败: {result['error']}")

    return result


if __name__ == "__main__":
    # 直接在IDE中运行
    print("="*60)
    print("🚀 电池OCV-SOC曲线生成器")
    print("="*60)

    # 直接调用主函数
    main()