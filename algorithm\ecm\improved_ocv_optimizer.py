#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的OCV优化器 - 解决SOC曲线拟合问题
"""

import numpy as np
import pandas as pd
from scipy import interpolate
from scipy.optimize import minimize
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

class ImprovedOCVOptimizer:
    """改进的OCV优化器"""
    
    def __init__(self, data, current_col, voltage_col, soc_col, n_soc_points=30):
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col
        self.soc_col = soc_col
        self.n_soc_points = n_soc_points
        
        # 数据预处理
        self._preprocess_data()
        
        # 提取SOC采样点
        self._extract_soc_points()
        
        # 估算参数边界
        self._estimate_bounds()
        
    def _preprocess_data(self):
        """数据预处理"""
        print('🔧 数据预处理...')
        
        # 移除异常值
        data_clean = self.data.copy()
        
        # 电压异常值过滤 (3σ原则)
        voltage_mean = data_clean[self.voltage_col].mean()
        voltage_std = data_clean[self.voltage_col].std()
        voltage_mask = (
            (data_clean[self.voltage_col] >= voltage_mean - 3*voltage_std) &
            (data_clean[self.voltage_col] <= voltage_mean + 3*voltage_std)
        )
        
        # SOC范围过滤
        soc_mask = (
            (data_clean[self.soc_col] >= 0) &
            (data_clean[self.soc_col] <= 100)
        )
        
        # 电流异常值过滤
        current_mean = data_clean[self.current_col].mean()
        current_std = data_clean[self.current_col].std()
        current_mask = (
            (data_clean[self.current_col] >= current_mean - 3*current_std) &
            (data_clean[self.current_col] <= current_mean + 3*current_std)
        )
        
        # 应用所有过滤条件
        mask = voltage_mask & soc_mask & current_mask
        self.data_clean = data_clean[mask].copy()
        
        print(f'  原始数据: {len(self.data)} 条')
        print(f'  清理后: {len(self.data_clean)} 条')
        print(f'  过滤比例: {(1 - len(self.data_clean)/len(self.data))*100:.1f}%')
        
    def _extract_soc_points(self):
        """提取SOC采样点 - 改进版"""
        soc_values = self.data_clean[self.soc_col].values
        
        # 使用分位数采样，但在关键区域增加密度
        percentiles_base = np.linspace(0, 100, self.n_soc_points)
        
        # 在SOC变化较大的区域增加采样点
        soc_range = np.max(soc_values) - np.min(soc_values)
        if soc_range > 50:  # 如果SOC范围较大
            # 在中间区域增加采样密度
            middle_points = np.linspace(25, 75, self.n_soc_points//3)
            percentiles_base = np.concatenate([
                np.linspace(0, 25, self.n_soc_points//3),
                middle_points,
                np.linspace(75, 100, self.n_soc_points//3)
            ])
        
        self.soc_points = np.percentile(soc_values, percentiles_base)
        self.soc_points = np.unique(np.sort(self.soc_points))
        
        print(f'📊 SOC采样点分析:')
        print(f'  采样点数: {len(self.soc_points)}')
        print(f'  SOC范围: {self.soc_points[0]:.1f}% ~ {self.soc_points[-1]:.1f}%')
        
    def _estimate_bounds(self):
        """估算参数边界"""
        voltage_values = self.data_clean[self.voltage_col].values
        
        # OCV边界 - 更保守的估算
        v_min = np.percentile(voltage_values, 1)
        v_max = np.percentile(voltage_values, 99)
        self.ocv_bounds = (v_min - 0.02, v_max + 0.05)
        
        # R0边界 - 基于物理合理性
        self.r0_bounds = (0.0001, 0.01)  # 0.1mΩ到10mΩ
        
        print(f'📈 参数边界:')
        print(f'  OCV: {self.ocv_bounds[0]:.3f}V ~ {self.ocv_bounds[1]:.3f}V')
        print(f'  R0: {self.r0_bounds[0]:.4f}Ω ~ {self.r0_bounds[1]:.4f}Ω')
        
    def _objective_function(self, params):
        """改进的目标函数"""
        n_soc = len(self.soc_points)
        ocv_values = params[:n_soc]
        r0_values = params[n_soc:]
        
        # 基本拟合误差
        try:
            # 插值函数
            ocv_interp = interpolate.interp1d(self.soc_points, ocv_values, 
                                            kind='linear', fill_value='extrapolate')
            r0_interp = interpolate.interp1d(self.soc_points, r0_values, 
                                           kind='linear', fill_value='extrapolate')
            
            # 计算预测电压
            soc_data = self.data_clean[self.soc_col].values
            current_data = self.data_clean[self.current_col].values
            voltage_actual = self.data_clean[self.voltage_col].values
            
            ocv_pred = ocv_interp(soc_data)
            r0_pred = r0_interp(soc_data)
            voltage_pred = ocv_pred - current_data * r0_pred
            
            # 主要误差 - 使用RMSE而非MAE
            mse = np.mean((voltage_actual - voltage_pred)**2)
            rmse = np.sqrt(mse)
            
            # 约束惩罚
            penalty = 0
            
            # 1. OCV单调性约束 (强化)
            ocv_diff = np.diff(ocv_values)
            monotonic_penalty = np.sum(np.maximum(0, -ocv_diff)) * 1000
            penalty += monotonic_penalty
            
            # 2. 平滑性约束 (新增)
            ocv_second_diff = np.diff(ocv_values, n=2)
            smoothness_penalty = np.sum(ocv_second_diff**2) * 10
            penalty += smoothness_penalty
            
            # 3. R0平滑性约束
            r0_second_diff = np.diff(r0_values, n=2)
            r0_smoothness_penalty = np.sum(r0_second_diff**2) * 100
            penalty += r0_smoothness_penalty
            
            # 4. 边界约束
            ocv_bound_penalty = (
                np.sum(np.maximum(0, self.ocv_bounds[0] - ocv_values)) +
                np.sum(np.maximum(0, ocv_values - self.ocv_bounds[1]))
            ) * 1000
            penalty += ocv_bound_penalty
            
            r0_bound_penalty = (
                np.sum(np.maximum(0, self.r0_bounds[0] - r0_values)) +
                np.sum(np.maximum(0, r0_values - self.r0_bounds[1]))
            ) * 1000
            penalty += r0_bound_penalty
            
            return rmse + penalty
            
        except Exception as e:
            return 1000.0  # 高惩罚值
    
    def optimize_with_scipy(self):
        """使用scipy优化"""
        print('🚀 开始scipy优化...')
        
        n_soc = len(self.soc_points)
        
        # 初始参数 - 更好的初始化
        ocv_init = np.linspace(self.ocv_bounds[0], self.ocv_bounds[1], n_soc)
        r0_init = np.full(n_soc, 0.002)  # 2mΩ初始值
        
        x0 = np.concatenate([ocv_init, r0_init])
        
        # 参数边界
        bounds = []
        for i in range(n_soc):
            bounds.append(self.ocv_bounds)
        for i in range(n_soc):
            bounds.append(self.r0_bounds)
        
        # 约束条件 - OCV单调性
        constraints = []
        for i in range(n_soc - 1):
            constraints.append({
                'type': 'ineq',
                'fun': lambda x, i=i: x[i+1] - x[i]  # OCV[i+1] >= OCV[i]
            })
        
        # 优化
        result = minimize(
            self._objective_function,
            x0,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 1000, 'disp': True}
        )
        
        if result.success:
            best_params = result.x
            best_rmse = result.fun
            
            print(f'✅ 优化成功!')
            print(f'  最佳RMSE: {best_rmse:.6f}V')
            print(f'  迭代次数: {result.nit}')
            
            return best_params, best_rmse
        else:
            print(f'❌ 优化失败: {result.message}')
            return None, None
    
    def plot_results(self, best_params, save_path=None):
        """绘制结果"""
        if best_params is None:
            return
            
        n_soc = len(self.soc_points)
        ocv_values = best_params[:n_soc]
        r0_values = best_params[n_soc:]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. OCV曲线
        axes[0,0].plot(self.soc_points, ocv_values, 'bo-', linewidth=2, markersize=6)
        axes[0,0].set_xlabel('SOC (%)')
        axes[0,0].set_ylabel('OCV (V)')
        axes[0,0].set_title('OCV-SOC曲线')
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. R0曲线
        axes[0,1].plot(self.soc_points, r0_values*1000, 'ro-', linewidth=2, markersize=6)
        axes[0,1].set_xlabel('SOC (%)')
        axes[0,1].set_ylabel('R0 (mΩ)')
        axes[0,1].set_title('内阻-SOC曲线')
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 拟合效果
        ocv_interp = interpolate.interp1d(self.soc_points, ocv_values, 
                                        kind='linear', fill_value='extrapolate')
        r0_interp = interpolate.interp1d(self.soc_points, r0_values, 
                                       kind='linear', fill_value='extrapolate')
        
        soc_data = self.data_clean[self.soc_col].values
        current_data = self.data_clean[self.current_col].values
        voltage_actual = self.data_clean[self.voltage_col].values
        
        ocv_pred = ocv_interp(soc_data)
        r0_pred = r0_interp(soc_data)
        voltage_pred = ocv_pred - current_data * r0_pred
        
        # 随机采样显示
        sample_idx = np.random.choice(len(voltage_actual), 1000, replace=False)
        axes[1,0].scatter(voltage_actual[sample_idx], voltage_pred[sample_idx], 
                         alpha=0.6, s=20)
        min_v = min(voltage_actual.min(), voltage_pred.min())
        max_v = max(voltage_actual.max(), voltage_pred.max())
        axes[1,0].plot([min_v, max_v], [min_v, max_v], 'r--', linewidth=2)
        axes[1,0].set_xlabel('实际电压 (V)')
        axes[1,0].set_ylabel('预测电压 (V)')
        axes[1,0].set_title('电压拟合效果')
        axes[1,0].grid(True, alpha=0.3)
        
        # 4. 误差分布
        errors = voltage_actual - voltage_pred
        axes[1,1].hist(errors, bins=50, alpha=0.7, edgecolor='black')
        axes[1,1].axvline(0, color='red', linestyle='--', linewidth=2)
        axes[1,1].set_xlabel('预测误差 (V)')
        axes[1,1].set_ylabel('频次')
        axes[1,1].set_title(f'误差分布 (RMSE={np.sqrt(np.mean(errors**2)):.4f}V)')
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f'📊 结果已保存到: {save_path}')
        
        plt.show()

def test_improved_optimizer():
    """测试改进的优化器"""
    print('🧪 测试改进的OCV优化器')
    print('='*50)
    
    # 加载数据
    data = pd.read_hdf('database/127_1_1_2023-12-25.h5')
    
    # 查找列名
    current_col = None
    voltage_col = None  
    soc_col = None

    for col in data.columns:
        if '2023-12-25_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif '2023-12-25_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
        elif '2023-12-25_1#BMS-堆内-1簇组SOC' in col:
            soc_col = col

    # 创建清理后的数据
    clean_data = pd.DataFrame({
        '电流': data[current_col],
        '电压': data[voltage_col],
        'SOC': data[soc_col]
    }, index=data.index)

    clean_data = clean_data.dropna()
    
    # 使用部分数据测试
    test_data = clean_data.sample(n=8000, random_state=42)
    
    # 创建改进的优化器
    optimizer = ImprovedOCVOptimizer(
        data=test_data,
        current_col='电流',
        voltage_col='电压',
        soc_col='SOC',
        n_soc_points=25
    )
    
    # 运行优化
    best_params, best_rmse = optimizer.optimize_with_scipy()
    
    if best_params is not None:
        # 绘制结果
        optimizer.plot_results(best_params, 'improved_ocv_results.png')
    
    return optimizer, best_params, best_rmse

if __name__ == "__main__":
    optimizer, best_params, best_rmse = test_improved_optimizer()
