#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据驱动OCV优化器使用示例

这个示例展示了如何使用基于实际SOC数据的OCV优化器，
相比传统的预设SOC点方法，这种方法更贴近实际数据分布。

🚀 主要特点:
1. 自动从数据中提取SOC采样点
2. 基于实际SOC分布进行优化
3. 支持CUDA加速
4. 提供详细的结果分析和可视化

📁 使用方法:
python example_data_driven_usage.py
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
from data_driven_ocv_optimizer import DataDrivenOCVOptimizer

def load_battery_data(date):
    """加载指定日期的电池数据"""
    file_path = f"database/127_1_1_{date}.h5"
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"数据文件不存在: {file_path}")
    
    print(f"📂 加载数据文件: {file_path}")
    data = pd.read_hdf(file_path)
    
    # 查找数据列
    current_col = None
    voltage_col = None
    soc_col = None
    
    for col in data.columns:
        if f'{date}_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif f'{date}_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
        elif f'{date}_1#BMS-堆内-1簇组SOC' in col:
            soc_col = col
    
    if not all([current_col, voltage_col, soc_col]):
        raise ValueError(f"找不到必要的数据列: {date}")
    
    # 重命名数据列
    clean_data = pd.DataFrame({
        '电流': data[current_col],
        '电压': data[voltage_col], 
        'SOC': data[soc_col]
    }, index=data.index)
    
    # 数据清理
    clean_data = clean_data.dropna()
    
    print(f"✅ 数据加载完成: {len(clean_data)} 条有效记录")
    print(f"   电流范围: {clean_data['电流'].min():.2f}A ~ {clean_data['电流'].max():.2f}A")
    print(f"   电压范围: {clean_data['电压'].min():.3f}V ~ {clean_data['电压'].max():.3f}V")
    print(f"   SOC范围: {clean_data['SOC'].min():.1f}% ~ {clean_data['SOC'].max():.1f}%")
    
    return clean_data

def compare_methods(data):
    """对比传统方法和数据驱动方法"""
    print("\n" + "="*60)
    print("🔍 方法对比分析")
    print("="*60)
    
    # 分析数据中的SOC分布
    soc_values = data['SOC'].values
    soc_unique = np.unique(soc_values)
    soc_coverage = np.max(soc_values) - np.min(soc_values)
    
    print(f"📊 数据SOC分析:")
    print(f"   唯一SOC值数量: {len(soc_unique)}")
    print(f"   SOC覆盖范围: {soc_coverage:.1f}%")
    print(f"   SOC分布密度: {len(soc_unique)/soc_coverage:.2f} 点/百分点")
    
    # 传统预设SOC点
    traditional_soc_points = np.array([
        0.0, 1.0, 2.0, 3.0, 5.0, 7.0, 10.0, 15.0, 20.0,
        30.0, 40.0, 50.0, 60.0, 70.0, 80.0,
        85.0, 90.0, 93.0, 95.0, 97.0, 98.0, 99.0, 100.0
    ])
    
    # 数据驱动SOC点（模拟提取过程）
    percentiles = np.linspace(0, 100, 25)
    data_driven_soc_points = np.percentile(soc_values, percentiles)
    data_driven_soc_points = np.unique(data_driven_soc_points)
    
    print(f"\n📋 SOC采样点对比:")
    print(f"   传统预设方法: {len(traditional_soc_points)} 个点")
    print(f"   数据驱动方法: {len(data_driven_soc_points)} 个点")
    
    # 分析覆盖情况
    soc_min, soc_max = np.min(soc_values), np.max(soc_values)
    
    traditional_in_range = traditional_soc_points[
        (traditional_soc_points >= soc_min) & (traditional_soc_points <= soc_max)
    ]
    traditional_out_range = traditional_soc_points[
        (traditional_soc_points < soc_min) | (traditional_soc_points > soc_max)
    ]
    
    print(f"\n🎯 覆盖分析:")
    print(f"   传统方法在数据范围内: {len(traditional_in_range)} 个点")
    print(f"   传统方法在数据范围外: {len(traditional_out_range)} 个点")
    print(f"   数据驱动方法完全覆盖: {len(data_driven_soc_points)} 个点")
    
    if len(traditional_out_range) > 0:
        print(f"   ⚠️  传统方法的无效点: {traditional_out_range}")

def run_optimization_example():
    """运行优化示例"""
    print("="*60)
    print("🚀 数据驱动OCV优化器示例")
    print("="*60)
    
    # 可用的测试日期
    test_dates = [
        "2023-12-25", "2023-12-26", "2023-12-27", 
        "2023-12-28", "2023-12-29", "2023-12-30", "2024-01-01"
    ]
    
    # 选择第一个可用的日期
    data = None
    selected_date = None
    
    for date in test_dates:
        try:
            data = load_battery_data(date)
            selected_date = date
            break
        except (FileNotFoundError, ValueError) as e:
            print(f"⚠️  跳过 {date}: {e}")
            continue
    
    if data is None:
        print("❌ 没有找到可用的数据文件")
        return
    
    print(f"\n✅ 使用数据: {selected_date}")
    
    # 方法对比分析
    compare_methods(data)
    
    # 创建数据驱动优化器
    print(f"\n🔧 创建数据驱动优化器...")
    optimizer = DataDrivenOCVOptimizer(
        data=data,
        current_col='电流',
        voltage_col='电压',
        soc_col='SOC',
        use_cuda=True  # 尝试使用CUDA加速
    )
    
    # 运行优化（使用较少的迭代次数用于演示）
    print(f"\n🚀 开始优化...")
    optimizer.generations = 100  # 减少迭代次数用于快速演示
    optimizer.population_size = 50
    
    best_params, best_mae, fitness_history = optimizer.optimize_parameters()
    
    # 生成结果报告
    print(f"\n📊 生成结果报告...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = f"data_driven_results_{selected_date}_{timestamp}.png"
    
    optimizer.plot_results(best_params, save_path)
    
    # 保存参数结果
    results_dict = {
        'date': selected_date,
        'timestamp': timestamp,
        'best_mae': float(best_mae),
        'soc_points': optimizer.soc_points.tolist(),
        'best_params': best_params,
        'fitness_history': fitness_history,
        'data_soc_range': optimizer.data_soc_range,
        'ocv_bounds': optimizer.ocv_bounds
    }
    
    import json
    results_file = f"data_driven_params_{selected_date}_{timestamp}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results_dict, f, indent=2, ensure_ascii=False)
    
    print(f"💾 参数结果已保存: {results_file}")
    
    return optimizer, best_params, best_mae

def analyze_soc_distribution(data):
    """分析SOC数据分布"""
    import matplotlib.pyplot as plt
    
    soc_values = data['SOC'].values
    
    plt.figure(figsize=(12, 8))
    
    # SOC分布直方图
    plt.subplot(2, 2, 1)
    plt.hist(soc_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.xlabel('SOC (%)')
    plt.ylabel('频次')
    plt.title('SOC分布直方图')
    plt.grid(True, alpha=0.3)
    
    # SOC时间序列
    plt.subplot(2, 2, 2)
    plt.plot(range(len(soc_values)), soc_values, 'b-', alpha=0.7, linewidth=1)
    plt.xlabel('时间点')
    plt.ylabel('SOC (%)')
    plt.title('SOC时间序列')
    plt.grid(True, alpha=0.3)
    
    # SOC vs 电压散点图
    plt.subplot(2, 2, 3)
    plt.scatter(soc_values, data['电压'].values, alpha=0.3, s=1)
    plt.xlabel('SOC (%)')
    plt.ylabel('电压 (V)')
    plt.title('SOC vs 电压分布')
    plt.grid(True, alpha=0.3)
    
    # SOC vs 电流散点图
    plt.subplot(2, 2, 4)
    plt.scatter(soc_values, data['电流'].values, alpha=0.3, s=1, color='red')
    plt.xlabel('SOC (%)')
    plt.ylabel('电流 (A)')
    plt.title('SOC vs 电流分布')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('soc_distribution_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 SOC分布分析图已保存: soc_distribution_analysis.png")

if __name__ == "__main__":
    try:
        # 运行主要示例
        optimizer, best_params, best_mae = run_optimization_example()
        
        # 如果成功，进行额外分析
        if optimizer is not None:
            print(f"\n🔍 进行SOC分布分析...")
            analyze_soc_distribution(optimizer.data)
            
        print(f"\n✅ 示例运行完成!")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
