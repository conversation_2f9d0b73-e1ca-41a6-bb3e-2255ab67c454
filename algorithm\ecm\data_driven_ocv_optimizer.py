#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于实际SOC数据的OCV-SOC曲线优化器
使用数据中真实的SOC分布进行参数辨识，而不是预设SOC点

🚀 主要改进:
1. 从数据中自动提取SOC采样点
2. 基于实际SOC分布进行优化
3. 避免在无数据区域的无效计算
4. 提高模型的实际适用性

📁 使用方法:
optimizer = DataDrivenOCVOptimizer(data, current_col, voltage_col, soc_col)
best_params, best_mae = optimizer.optimize_parameters()
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime
import json
from scipy.interpolate import interp1d
import time
import logging
from sklearn.cluster import KMeans

try:
    import cupy as cp
    CUDA_AVAILABLE = True
    print("CUDA支持可用")
except ImportError:
    print("CuPy未安装，将使用CPU版本")
    import numpy as cp
    CUDA_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DataDrivenConfig:
    """数据驱动优化器配置"""
    # 电池模型参数
    CAPACITY_AH = 280
    TARGET_CELL = 1
    
    # SOC采样参数
    MAX_SOC_POINTS = 25  # 最大SOC采样点数
    MIN_SOC_POINTS = 10  # 最小SOC采样点数
    SOC_COVERAGE_THRESHOLD = 30  # SOC覆盖范围阈值(%)
    
    # R0参数
    R0_SOC_POINTS = np.array([0.0, 25.0, 50.0, 75.0, 100.0])
    DEFAULT_R0 = np.array([0.0008, 0.0005, 0.0002, 0.0005, 0.0008])
    R0_BOUNDS_OFFSET = 0.0003
    
    # GA参数
    POPULATION_SIZE = 200
    GENERATIONS = 500
    MUTATION_RATE = 0.1
    CROSSOVER_RATE = 0.8
    ELITE_RATIO = 0.15

class DataDrivenOCVOptimizer:
    """基于实际数据的OCV优化器"""
    
    def __init__(self, data, current_col, voltage_col, soc_col, use_cuda=True):
        """
        初始化数据驱动的OCV优化器
        
        Args:
            data: 包含电流、电压、SOC的数据
            current_col: 电流列名
            voltage_col: 电压列名  
            soc_col: SOC列名
            use_cuda: 是否使用CUDA加速
        """
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col
        self.soc_col = soc_col
        self.use_cuda = use_cuda and CUDA_AVAILABLE
        
        # 从数据中提取SOC采样点
        self.soc_points = self._extract_soc_points_from_data()
        self.data_soc_range = self._analyze_soc_distribution()
        
        # 电池模型参数
        self.capacity_Ah = DataDrivenConfig.CAPACITY_AH
        self.r0_soc_points = DataDrivenConfig.R0_SOC_POINTS
        self.default_r0 = DataDrivenConfig.DEFAULT_R0
        self.r0_bounds = (self.default_r0 - DataDrivenConfig.R0_BOUNDS_OFFSET,
                         self.default_r0 + DataDrivenConfig.R0_BOUNDS_OFFSET)
        
        # GA参数
        self.population_size = DataDrivenConfig.POPULATION_SIZE
        self.generations = DataDrivenConfig.GENERATIONS
        self.mutation_rate = DataDrivenConfig.MUTATION_RATE
        self.crossover_rate = DataDrivenConfig.CROSSOVER_RATE
        self.elite_ratio = DataDrivenConfig.ELITE_RATIO
        
        # 根据数据估算OCV边界
        self.ocv_bounds = self._estimate_ocv_bounds()
        
        print(f"📊 数据驱动SOC分析完成:")
        print(f"   SOC采样点数: {len(self.soc_points)}")
        print(f"   SOC范围: {self.data_soc_range['min']:.1f}% - {self.data_soc_range['max']:.1f}%")
        print(f"   SOC覆盖度: {self.data_soc_range['coverage']:.1f}%")
        print(f"   OCV估算范围: {self.ocv_bounds[0]:.3f}V - {self.ocv_bounds[1]:.3f}V")
        
        if self.use_cuda:
            self._transfer_data_to_gpu()
    
    def _extract_soc_points_from_data(self):
        """从实际数据中提取SOC采样点"""
        print("🔍 从数据中提取SOC采样点...")
        
        # 获取实际SOC数据
        actual_soc = self.data[self.soc_col].values
        
        # 数据清理
        valid_mask = ~np.isnan(actual_soc)
        valid_mask &= (actual_soc >= 0) & (actual_soc <= 100)
        valid_soc = actual_soc[valid_mask]
        
        if len(valid_soc) == 0:
            raise ValueError("没有有效的SOC数据")
        
        # 方法1: 使用分位数采样（确保覆盖整个范围）
        n_points = min(DataDrivenConfig.MAX_SOC_POINTS, 
                      max(DataDrivenConfig.MIN_SOC_POINTS, len(np.unique(valid_soc))//10))
        
        percentiles = np.linspace(0, 100, n_points)
        soc_points = np.percentile(valid_soc, percentiles)
        
        # 去重并排序
        soc_points = np.unique(soc_points)
        soc_points = np.sort(soc_points)
        
        # 确保边界点
        soc_min, soc_max = np.min(valid_soc), np.max(valid_soc)
        if soc_points[0] > soc_min:
            soc_points = np.insert(soc_points, 0, soc_min)
        if soc_points[-1] < soc_max:
            soc_points = np.append(soc_points, soc_max)
        
        print(f"   提取到 {len(soc_points)} 个SOC采样点")
        print(f"   SOC点范围: {soc_points[0]:.1f}% - {soc_points[-1]:.1f}%")
        
        return soc_points
    
    def _analyze_soc_distribution(self):
        """分析SOC数据分布"""
        actual_soc = self.data[self.soc_col].values
        valid_soc = actual_soc[~np.isnan(actual_soc)]
        
        soc_info = {
            'min': np.min(valid_soc),
            'max': np.max(valid_soc),
            'mean': np.mean(valid_soc),
            'std': np.std(valid_soc),
            'coverage': np.max(valid_soc) - np.min(valid_soc),
            'data_points': len(valid_soc)
        }
        
        # 检查SOC覆盖度
        if soc_info['coverage'] < DataDrivenConfig.SOC_COVERAGE_THRESHOLD:
            print(f"⚠️  警告: SOC覆盖范围较小 ({soc_info['coverage']:.1f}%), 可能影响OCV曲线精度")
        
        return soc_info
    
    def _estimate_ocv_bounds(self):
        """基于数据估算OCV边界"""
        # 获取电压数据的统计信息
        voltage_data = self.data[self.voltage_col].values
        valid_voltage = voltage_data[~np.isnan(voltage_data)]
        
        v_min = np.percentile(valid_voltage, 5)   # 5%分位数
        v_max = np.percentile(valid_voltage, 95)  # 95%分位数
        
        # OCV通常比端电压稍高（考虑内阻压降）
        ocv_min = max(2.5, v_min - 0.1)  # 最低2.5V
        ocv_max = min(4.0, v_max + 0.2)  # 最高4.0V
        
        return (ocv_min, ocv_max)
    
    def _transfer_data_to_gpu(self):
        """将数据转移到GPU"""
        if not self.use_cuda:
            return
            
        print("🚀 转移数据到GPU...")
        
        # 转移时间数据
        time_values = self.data.index.values
        time_numeric = time_values.astype('datetime64[ns]').astype('int64')
        self.gpu_time_values = cp.asarray(time_numeric)
        
        # 转移电流、电压、SOC数据
        self.gpu_current = cp.asarray(self.data[self.current_col].values)
        self.gpu_voltage = cp.asarray(self.data[self.voltage_col].values)
        self.gpu_actual_soc = cp.asarray(self.data[self.soc_col].values)
        
        # 预计算时间差
        time_diff = cp.diff(self.gpu_time_values)
        self.gpu_delta_t = cp.zeros(len(self.data))
        self.gpu_delta_t[1:] = time_diff / (1e9 * 3600)  # 转换为小时
        
        # 转移SOC采样点
        self.gpu_soc_points = cp.asarray(self.soc_points)
        self.gpu_r0_soc_points = cp.asarray(self.r0_soc_points)
        
        print(f"   数据转移完成: {len(self.data)} 条记录")
    
    def _initialize_population(self):
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            individual = []
            
            # 初始SOC：基于数据中的实际SOC范围
            soc_range = self.data_soc_range
            initial_soc = np.random.uniform(
                max(0, soc_range['min'] - 2), 
                min(100, soc_range['max'] + 2)
            )
            individual.append(initial_soc)
            
            # OCV点：基于估算的边界，确保单调递增
            ocv_points = []
            ocv_min, ocv_max = self.ocv_bounds
            
            # 第一个OCV点
            ocv_points.append(np.random.uniform(ocv_min, ocv_min + 0.2))
            
            # 后续OCV点，确保单调递增
            for i in range(1, len(self.soc_points)):
                min_ocv = ocv_points[-1] + 0.001
                max_ocv = min(ocv_max, min_ocv + 0.3)
                ocv_points.append(np.random.uniform(min_ocv, max_ocv))
            
            individual.extend(ocv_points)
            
            # R0点
            for _ in range(len(self.r0_soc_points)):
                individual.append(np.random.uniform(0.0001, 0.0015))
            
            population.append(individual)
        
        return population
    
    def simulate_battery(self, params):
        """电池模拟（支持CPU和GPU）"""
        if self.use_cuda:
            return self._cuda_simulate_battery(params)
        else:
            return self._cpu_simulate_battery(params)
    
    def _cpu_simulate_battery(self, params):
        """CPU版本的电池模拟"""
        initial_soc = params[0]
        ocv_points = params[1:len(self.soc_points)+1]
        r0_points = params[len(self.soc_points)+1:]
        
        # 创建插值函数
        ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear', 
                             fill_value='extrapolate')
        r0_interp = interp1d(self.r0_soc_points, r0_points, kind='linear',
                            fill_value='extrapolate')
        
        # 计算时间差
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')
        
        # 计算SOC变化
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / self.capacity_Ah * 100
        soc = initial_soc - np.cumsum(delta_soc)
        soc = np.clip(soc, 0, 100)
        
        # 计算端电压
        ocv = ocv_interp(soc)
        r0 = r0_interp(soc)
        voltage_sim = ocv - current_values * r0
        
        # 计算误差
        errors = voltage_sim - self.data[self.voltage_col].values
        mae = np.mean(np.abs(errors))
        
        return mae
    
    def _cuda_simulate_battery(self, params):
        """GPU版本的电池模拟"""
        # 转移参数到GPU
        gpu_params = cp.asarray(params)
        
        initial_soc = gpu_params[0]
        ocv_points = gpu_params[1:len(self.soc_points)+1]
        r0_points = gpu_params[len(self.soc_points)+1:]
        
        # 计算SOC变化
        delta_soc = self.gpu_current * self.gpu_delta_t / self.capacity_Ah * 100
        soc = initial_soc - cp.cumsum(delta_soc)
        soc = cp.clip(soc, 0, 100)
        
        # 插值计算OCV和R0
        ocv = cp.interp(soc, self.gpu_soc_points, ocv_points)
        r0 = cp.interp(soc, self.gpu_r0_soc_points, r0_points)
        
        # 计算端电压
        voltage_sim = ocv - self.gpu_current * r0
        
        # 计算误差
        errors = voltage_sim - self.gpu_voltage
        mae = cp.mean(cp.abs(errors))
        
        return float(mae)

    def optimize_parameters(self):
        """运行参数优化"""
        print(f"\n🚀 开始数据驱动的OCV参数优化...")
        print(f"   种群大小: {self.population_size}")
        print(f"   迭代次数: {self.generations}")
        print(f"   SOC采样点: {len(self.soc_points)}")
        print(f"   计算模式: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")

        start_time = time.time()

        # 初始化种群
        population = self._initialize_population()
        best_fitness_history = []

        for generation in range(self.generations):
            gen_start = time.time()

            # 评估适应度
            fitness_values = []
            for individual in population:
                try:
                    fitness = self.simulate_battery(individual)
                    # 添加约束惩罚
                    fitness += self._calculate_constraints_penalty(individual)
                    fitness_values.append(fitness)
                except Exception as e:
                    fitness_values.append(1000.0)  # 高惩罚值

            fitness_values = np.array(fitness_values)

            # 选择、交叉、变异
            population = self._evolve_population(population, fitness_values)

            # 记录最佳适应度
            best_fitness = np.min(fitness_values)
            best_fitness_history.append(best_fitness)

            gen_time = time.time() - gen_start

            # 早停检测
            if generation > 50 and len(best_fitness_history) > 20:
                recent_improvement = best_fitness_history[-20] - best_fitness_history[-1]
                if recent_improvement < 0.0001:
                    print(f"🛑 早停触发: 第{generation}代")
                    break

            if generation % 50 == 0 or generation == self.generations - 1:
                print(f"   第 {generation:3d} 代: 最佳MAE={best_fitness:.6f}V, 用时={gen_time:.2f}s")

        # 找到最优解
        final_fitness = [self.simulate_battery(ind) for ind in population]
        best_idx = np.argmin(final_fitness)
        best_params = population[best_idx]
        best_mae = final_fitness[best_idx]

        total_time = time.time() - start_time

        print(f"\n✅ 数据驱动优化完成!")
        print(f"   最佳MAE: {best_mae:.6f}V")
        print(f"   总用时: {total_time:.1f}秒")
        print(f"   实际迭代: {len(best_fitness_history)}代")

        return best_params, best_mae, best_fitness_history

    def _calculate_constraints_penalty(self, params):
        """计算约束惩罚"""
        penalty = 0.0

        initial_soc = params[0]
        ocv_points = params[1:len(self.soc_points)+1]
        r0_points = params[len(self.soc_points)+1:]

        # SOC约束
        if initial_soc < 0 or initial_soc > 100:
            penalty += 10.0

        # OCV边界约束
        ocv_min, ocv_max = self.ocv_bounds
        for ocv in ocv_points:
            if ocv < ocv_min or ocv > ocv_max:
                penalty += 5.0

        # OCV单调性约束
        ocv_diff = np.diff(ocv_points)
        if not np.all(ocv_diff >= 0):
            penalty += 2.0 * np.sum(np.abs(ocv_diff[ocv_diff < 0]))

        # R0约束
        for r0 in r0_points:
            if r0 < 0.0001 or r0 > 0.002:
                penalty += 5.0

        return penalty

    def _evolve_population(self, population, fitness_values):
        """进化种群"""
        # 精英选择
        elite_count = int(self.population_size * self.elite_ratio)
        elite_indices = np.argsort(fitness_values)[:elite_count]
        new_population = [population[i] for i in elite_indices]

        # 生成剩余个体
        while len(new_population) < self.population_size:
            # 锦标赛选择
            parent1 = self._tournament_selection(population, fitness_values)
            parent2 = self._tournament_selection(population, fitness_values)

            # 交叉
            if np.random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()

            # 变异
            child1 = self._mutate(child1)
            child2 = self._mutate(child2)

            new_population.extend([child1, child2])

        return new_population[:self.population_size]

    def _tournament_selection(self, population, fitness_values, tournament_size=3):
        """锦标赛选择"""
        indices = np.random.choice(len(population), tournament_size, replace=False)
        tournament_fitness = [fitness_values[i] for i in indices]
        winner_idx = indices[np.argmin(tournament_fitness)]
        return population[winner_idx].copy()

    def _crossover(self, parent1, parent2):
        """单点交叉"""
        crossover_point = np.random.randint(1, len(parent1))
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        return child1, child2

    def _mutate(self, individual):
        """高斯变异"""
        mutated = individual.copy()

        for i in range(len(individual)):
            if np.random.random() < self.mutation_rate:
                if i == 0:  # 初始SOC
                    mutated[i] += np.random.normal(0, 1.0)
                    mutated[i] = np.clip(mutated[i], 0, 100)
                elif 1 <= i <= len(self.soc_points):  # OCV点
                    mutated[i] += np.random.normal(0, 0.02)
                    ocv_min, ocv_max = self.ocv_bounds
                    mutated[i] = np.clip(mutated[i], ocv_min, ocv_max)
                else:  # R0点
                    mutated[i] += np.random.normal(0, 0.0001)
                    mutated[i] = np.clip(mutated[i], 0.0001, 0.002)

        # 确保OCV单调递增
        ocv_start = 1
        ocv_end = len(self.soc_points) + 1
        ocv_points = mutated[ocv_start:ocv_end]

        for j in range(1, len(ocv_points)):
            if ocv_points[j] < ocv_points[j-1]:
                ocv_points[j] = ocv_points[j-1] + 0.001

        mutated[ocv_start:ocv_end] = ocv_points
        return mutated

    def get_detailed_results(self, params):
        """获取详细的模拟结果"""
        initial_soc = params[0]
        ocv_points = params[1:len(self.soc_points)+1]
        r0_points = params[len(self.soc_points)+1:]

        # 创建插值函数
        ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear',
                             fill_value='extrapolate')
        r0_interp = interp1d(self.r0_soc_points, r0_points, kind='linear',
                            fill_value='extrapolate')

        # 计算时间差
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')

        # 计算SOC变化
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / self.capacity_Ah * 100
        soc_sim = initial_soc - np.cumsum(delta_soc)
        soc_sim = np.clip(soc_sim, 0, 100)

        # 计算端电压
        ocv_sim = ocv_interp(soc_sim)
        r0_sim = r0_interp(soc_sim)
        voltage_sim = ocv_sim - current_values * r0_sim

        # 计算误差指标
        voltage_actual = self.data[self.voltage_col].values
        errors = voltage_sim - voltage_actual
        mae = np.mean(np.abs(errors))
        rmse = np.sqrt(np.mean(errors**2))
        max_error = np.max(np.abs(errors))

        return {
            'soc_sim': soc_sim,
            'ocv_sim': ocv_sim,
            'r0_sim': r0_sim,
            'voltage_sim': voltage_sim,
            'voltage_actual': voltage_actual,
            'soc_actual': self.data[self.soc_col].values,
            'current': current_values,
            'errors': errors,
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error,
            'soc_points': self.soc_points,
            'ocv_points': ocv_points,
            'r0_soc_points': self.r0_soc_points,
            'r0_points': r0_points
        }

    def plot_results(self, params, save_path=None):
        """绘制优化结果"""
        results = self.get_detailed_results(params)

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('数据驱动OCV优化结果', fontsize=16, fontweight='bold')

        # 1. 电压对比
        ax1 = axes[0, 0]
        time_index = range(len(results['voltage_actual']))
        ax1.plot(time_index, results['voltage_actual'], 'b-', alpha=0.7,
                label='实测电压', linewidth=1)
        ax1.plot(time_index, results['voltage_sim'], 'r-', alpha=0.8,
                label=f'模拟电压 (MAE:{results["mae"]:.4f}V)', linewidth=1.5)
        ax1.set_xlabel('时间点')
        ax1.set_ylabel('电压 (V)')
        ax1.set_title('电压对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. SOC对比
        ax2 = axes[0, 1]
        ax2.plot(time_index, results['soc_actual'], 'g-', alpha=0.7,
                label='BMS SOC', linewidth=1)
        ax2.plot(time_index, results['soc_sim'], 'orange', alpha=0.8,
                label='模拟SOC', linewidth=1.5)
        ax2.set_xlabel('时间点')
        ax2.set_ylabel('SOC (%)')
        ax2.set_title('SOC对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. OCV-SOC曲线
        ax3 = axes[1, 0]
        ax3.plot(results['soc_points'], results['ocv_points'], 'ro-',
                linewidth=2, markersize=6, label='优化的OCV曲线')

        # 显示数据中的SOC-电压分布
        soc_actual = results['soc_actual']
        voltage_actual = results['voltage_actual']
        valid_mask = ~np.isnan(soc_actual) & ~np.isnan(voltage_actual)
        ax3.scatter(soc_actual[valid_mask], voltage_actual[valid_mask],
                   alpha=0.1, s=1, color='blue', label='实际数据点')

        ax3.set_xlabel('SOC (%)')
        ax3.set_ylabel('OCV (V)')
        ax3.set_title('OCV-SOC曲线')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. R0-SOC曲线
        ax4 = axes[1, 1]
        ax4.plot(results['r0_soc_points'], results['r0_points'], 'go-',
                linewidth=2, markersize=6, label='优化的R0曲线')
        ax4.set_xlabel('SOC (%)')
        ax4.set_ylabel('R0 (Ω)')
        ax4.set_title('R0-SOC曲线')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 结果图表已保存: {save_path}")

        plt.show()

        # 打印详细统计信息
        print(f"\n📊 优化结果统计:")
        print(f"   MAE: {results['mae']:.6f} V")
        print(f"   RMSE: {results['rmse']:.6f} V")
        print(f"   最大误差: {results['max_error']:.6f} V")
        print(f"   SOC采样点数: {len(results['soc_points'])}")
        print(f"   SOC范围: {results['soc_points'][0]:.1f}% - {results['soc_points'][-1]:.1f}%")
        print(f"   OCV范围: {np.min(results['ocv_points']):.3f}V - {np.max(results['ocv_points']):.3f}V")
        print(f"   R0范围: {np.min(results['r0_points']):.6f}Ω - {np.max(results['r0_points']):.6f}Ω")


def test_data_driven_optimizer():
    """测试数据驱动优化器"""
    print("="*60)
    print("数据驱动OCV优化器测试")
    print("="*60)

    # 加载测试数据
    print("📂 加载测试数据...")
    data_file = 'database/127_1_1_2023-12-25.h5'

    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return

    data = pd.read_hdf(data_file)

    # 查找数据列
    current_col = None
    voltage_col = None
    soc_col = None

    for col in data.columns:
        if '2023-12-25_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif '2023-12-25_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
        elif '2023-12-25_1#BMS-堆内-1簇组SOC' in col:
            soc_col = col

    if not all([current_col, voltage_col, soc_col]):
        print("❌ 找不到必要的数据列")
        return

    # 重命名数据
    test_data = pd.DataFrame({
        '电流': data[current_col],
        '电压': data[voltage_col],
        'SOC': data[soc_col]
    }, index=data.index)

    print(f"✅ 数据加载完成: {len(test_data)} 条记录")

    # 创建优化器
    optimizer = DataDrivenOCVOptimizer(
        data=test_data,
        current_col='电流',
        voltage_col='电压',
        soc_col='SOC',
        use_cuda=CUDA_AVAILABLE
    )

    # 运行优化
    best_params, best_mae, history = optimizer.optimize_parameters()

    # 绘制结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = f"data_driven_ocv_results_{timestamp}.png"
    optimizer.plot_results(best_params, save_path)

    return optimizer, best_params, best_mae


if __name__ == "__main__":
    # 运行测试
    test_data_driven_optimizer()
