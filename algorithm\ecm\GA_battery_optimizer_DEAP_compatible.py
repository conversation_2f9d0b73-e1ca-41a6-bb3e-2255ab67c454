import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import random
from collections import defaultdict

# DEAP库导入
from deap import base, creator, tools, algorithms

# 设置中文字体
import matplotlib.font_manager as fm
try:
    fm.fontManager.addfont('simhei.ttf')
    plt.rcParams['font.sans-serif'] = ['simhei']  # 必须与字体文件内部名称一致
except:
    # 如果字体文件不存在，使用系统默认字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class BatteryParameterOptimizer:
    def __init__(self, data, current_col, voltage_col):
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col
        
        # 更密集的SOC点 (0%-100%，每2%一个点)
        self.soc_points =np.array([
    0.0, 1.0, 2.0, 3.0, 5.0, 7.0, 10.0, 15.0, 20.0,   # 放电截止处密集 (0-20%)
    30.0, 40.0, 50.0, 60.0, 70.0, 80.0,               # 平台期稀疏 (20-80%)
    85.0, 90.0, 93.0, 95.0, 97.0, 98.0, 99.0, 100.0   # 充电截止处密集 (80-100%)
])
        
        # 定义R0 SOC点 (用于可变内阻插值)
        self.r0_soc_points = np.array([0.0, 25.0, 50.0, 75.0, 100.0])  # 在关键SOC点定义内阻
        # 默认R0值 (Ω)
        self.default_r0 = np.array([0.0008, 0.0006, 0.0005, 0.0006, 0.0008])  # 随SOC变化的内阻值
        self.r0_bounds = (self.default_r0 - 0.0003, self.default_r0 + 0.0003)
        
        # 默认OCV曲线和允许波动范围 (±0.3V)
        # 初始使用线性插值生成默认曲线，后面会被优化
        self.default_ocv = np.array([
    2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 3.10, 3.20,  # 对应放电截止的电压
    3.28, 3.30, 3.31, 3.32, 3.33, 3.34,                  # 对应平台期的电压
    3.36, 3.38, 3.40, 3.41, 3.42, 3.43, 3.44, 3.45        # 对应充电截止的电压
])
        self.ocv_bounds = (self.default_ocv - 0.3, self.default_ocv + 0.3)
        
        # 确保SOC点和OCV点长度一致
        assert len(self.soc_points) == len(self.default_ocv), "SOC点和OCV点长度必须一致"
        
        # 优化过程记录
        self.optimization_history = {
            'iteration': [],
            'best_fitness': [],
            'best_ocv': []
        }
        
        # GA参数
        # 遗传算法核心参数配置
        self.population_size = 150      # 种群大小：每一代包含300个个体（候选解）
        self.generations = 300          # 进化代数：算法运行200代进行优化
        self.mutation_rate = 0.1        # 变异率：10%的基因位发生随机变异，增加种群多样性
        self.crossover_rate = 0.7     # 交叉率：80%的个体参与交叉操作，产生新的后代
        self.elite_ratio = 0.15        # 精英比例：保留10%的最优个体直接进入下一代
        self.elite_size = int(self.population_size * self.elite_ratio)  # 精英个体数量：30个最优个体

        # 输出控制参数
        self.verbose = False  # 详细输出模式
        self.show_progress = True  # 显示进度
        
        # 初始化DEAP
        self._setup_deap()

    def _setup_deap(self):
        """设置DEAP遗传算法工具箱"""
        # 创建适应度类和个体类
        creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMin)
        
        self.toolbox = base.Toolbox()
        
        # 注册遗传算子
        self.toolbox.register("individual", self._create_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("evaluate", self._evaluate_individual)
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=3)

    def _create_individual(self):
        """创建一个个体（染色体）"""
        # 初始SOC在4-6%范围内
        initial_soc = np.random.uniform(4, 6)

        # OCV点在边界范围内随机生成
        ocv_points = np.random.uniform(self.ocv_bounds[0], self.ocv_bounds[1])

        # R0点在边界范围内随机生成
        r0_points = np.random.uniform(self.r0_bounds[0], self.r0_bounds[1])

        individual = creator.Individual(np.concatenate([[initial_soc], ocv_points, r0_points]))
        return individual

    def _evaluate_individual(self, individual):
        """评估个体适应度"""
        params = np.array(individual)
        fitness = self.fitness_function(params)
        return (fitness,)

    def _crossover(self, ind1, ind2):
        """单点交叉"""
        if random.random() > self.crossover_rate:
            return ind1, ind2

        # 随机选择交叉点
        crossover_point = random.randint(1, len(ind1) - 1)

        child1 = creator.Individual(np.concatenate([ind1[:crossover_point], ind2[crossover_point:]]))
        child2 = creator.Individual(np.concatenate([ind2[:crossover_point], ind1[crossover_point:]]))

        return child1, child2

    def _mutate(self, individual):
        """高斯变异"""
        mutated = individual[:]

        for i in range(len(individual)):
            if random.random() < self.mutation_rate:
                if i == 0:  # 初始SOC
                    noise = np.random.normal(0, 0.5)
                    mutated[i] = np.clip(mutated[i] + noise, 4, 6)
                elif i <= len(self.soc_points):  # OCV点
                    noise = np.random.normal(0, 0.05)
                    mutated[i] = np.clip(mutated[i] + noise,
                                       self.ocv_bounds[0][i-1],
                                       self.ocv_bounds[1][i-1])
                else:  # R0点
                    r0_index = i - len(self.soc_points) - 1
                    noise = np.random.normal(0, 0.0001)
                    mutated[i] = np.clip(mutated[i] + noise,
                                       self.r0_bounds[0][r0_index],
                                       self.r0_bounds[1][r0_index])

        return (creator.Individual(mutated),)

    def set_verbose_mode(self, verbose=True):
        """设置详细输出模式"""
        self.verbose = verbose
        if verbose:
            print("已开启详细输出模式，将显示完整的评价指标")
        else:
            print("已关闭详细输出模式，将显示简化指标")
    
    def simulate_battery(self, params, plot_comparison=False):
        """使用给定参数运行电池模拟"""
        initial_soc = params[0]
        ocv_points = params[1:len(self.soc_points)+1]  # OCV点参数
        r0_points = params[len(self.soc_points)+1:]   # R0点参数
        capacity_Ah = 224  # 固定容量
        
        # 验证参数长度
        assert len(ocv_points) == len(self.soc_points), "OCV点数量必须与SOC点相同"
        assert len(r0_points) == len(self.r0_soc_points), "R0点数量必须与R0 SOC点相同"
        
        try:
            # 创建OCV插值函数
            ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear',
                                 fill_value="extrapolate")
            # 创建R0插值函数
            r0_interp = interp1d(self.r0_soc_points, r0_points, kind='linear',
                                 fill_value="extrapolate")
        except ValueError as e:
            raise ValueError(f"插值函数创建失败: {str(e)}")
        
        # 计算时间差（小时）
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')
        
        # 计算SOC
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / capacity_Ah * 100
        soc = initial_soc - np.cumsum(delta_soc)
        soc = np.clip(soc, 0, 100)
        
        # 计算端电压 (使用可变R0)
        ocv = ocv_interp(soc)
        r0 = r0_interp(soc)
        voltage = ocv - current_values * r0
        
        # 计算评价指标
        errors = voltage - self.data[self.voltage_col]
        mae = np.mean(np.abs(errors))
        rmse = np.sqrt(np.mean(errors**2))
        max_error = np.max(np.abs(errors))

        # 绘图
        if plot_comparison:
            self._plot_comparison(voltage, soc, initial_soc, capacity_Ah, ocv_interp)

        # 如果需要详细指标，返回字典；否则只返回MAE保持兼容性
        if hasattr(self, 'return_detailed_metrics') and self.return_detailed_metrics:
            return {
                'mae': mae,
                'rmse': rmse,
                'max_error': max_error,
                'errors': errors,
                'voltage': voltage,
                'soc': soc
            }
        else:
            return mae
    
    def _plot_comparison(self, sim_voltage, sim_soc, initial_soc, capacity_Ah, ocv_interp):
        """绘制对比图"""
        plt.figure(figsize=(12, 10))
        
        # 电压对比
        plt.subplot(3, 1, 1)
        plt.plot(self.data.index, self.data[self.voltage_col], 'b-', label='真实电压')
        plt.plot(self.data.index, sim_voltage, 'r--', label='模拟电压')
        plt.ylabel('电压(V)')
        plt.title(f'电池模拟 (容量={capacity_Ah}Ah, 初始SOC={initial_soc}%)')
        plt.legend()
        plt.grid(True)
        
        # SOC变化
        plt.subplot(3, 1, 2)
        plt.plot(self.data.index, sim_soc, 'g-', label='模拟SOC')
        if '1#BMS-堆内-1簇组SOC' in self.data.columns:
            plt.plot(self.data.index, self.data['1#BMS-堆内-1簇组SOC'], 'r-', label='BMS_SOC')
        plt.ylabel('SOC(%)')
        plt.legend()
        plt.grid(True)
        
        # OCV变化
        plt.subplot(3, 1, 3)
        plt.plot(self.data.index, ocv_interp(sim_soc), 'm-', label='OCV')
        plt.xlabel('时间')
        plt.ylabel('OCV(V)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def fitness_function(self, params):
        """GA适应度函数，优化OCV曲线和R0曲线"""
        # 提取参数 (初始SOC + OCV点 + R0点)
        ocv_points = params[1:len(self.soc_points)+1]
        r0_points = params[len(self.soc_points)+1:]
        
        # 1. OCV约束检查
        ocv_penalty = 0
        
        # 检查OCV是否在允许范围内
        if not np.all((ocv_points >= self.ocv_bounds[0]) & (ocv_points <= self.ocv_bounds[1])):
            # 计算超出边界的惩罚
            lower_violation = np.sum(np.maximum(self.ocv_bounds[0] - ocv_points, 0))
            upper_violation = np.sum(np.maximum(ocv_points - self.ocv_bounds[1], 0))
            ocv_penalty += 100 * (lower_violation + upper_violation)
        
        # 检查OCV单调性
        ocv_diff = np.diff(ocv_points)
        if not np.all(ocv_diff >= 0):
            ocv_penalty +=  0.1*np.sum(np.abs(ocv_diff[ocv_diff < 0]))
        
        # 2. R0约束检查
        r0_penalty = 0
        
        # 检查R0是否在允许范围内
        if not np.all((r0_points >= self.r0_bounds[0]) & (r0_points <= self.r0_bounds[1])):
            # 计算超出边界的惩罚
            lower_violation = np.sum(np.maximum(self.r0_bounds[0] - r0_points, 0))
            upper_violation = np.sum(np.maximum(r0_points - self.r0_bounds[1], 0))
            r0_penalty += 100 * (lower_violation + upper_violation)
        
        # 检查R0单调性（在低SOC和高SOC区域应该增加）
        # 我们不强制R0单调，但可以添加轻微惩罚以避免过于剧烈的变化
        r0_diff = np.diff(r0_points)
        if np.any(np.abs(r0_diff) > 0.0005):  # 如果变化过大
            r0_penalty += 0.1 * np.sum(np.abs(r0_diff[np.abs(r0_diff) > 0.0005]))
        
        # 2. 运行模拟计算评价指标
        try:
            # 临时开启详细指标模式
            self.return_detailed_metrics = True
            metrics = self.simulate_battery(params)
            self.return_detailed_metrics = False

            mae = metrics['mae']
            rmse = metrics['rmse']
            max_error = metrics['max_error']

        except Exception as e:
            print(f"模拟失败: {str(e)}")
            return 1e6  # 返回大值表示无效解
        
        # 记录当前迭代信息
        current_iter = len(self.optimization_history['iteration']) + 1
        self.optimization_history['iteration'].append(current_iter)
        self.optimization_history['best_fitness'].append(mae + ocv_penalty + r0_penalty)
        self.optimization_history['best_ocv'].append(ocv_points.copy())
        
        # 计算详细的惩罚项分解
        boundary_penalty = 0
        monotonic_penalty = 0
        r0_boundary_penalty = 0

        # 重新计算边界惩罚（用于详细输出）
        if not np.all((ocv_points >= self.ocv_bounds[0]) & (ocv_points <= self.ocv_bounds[1])):
            lower_violation = np.sum(np.maximum(self.ocv_bounds[0] - ocv_points, 0))
            upper_violation = np.sum(np.maximum(ocv_points - self.ocv_bounds[1], 0))
            boundary_penalty = 100 * (lower_violation + upper_violation)

        # 重新计算单调性惩罚（用于详细输出）
        ocv_diff = np.diff(ocv_points)
        if not np.all(ocv_diff >= 0):
            monotonic_penalty = 0.05 * np.sum(np.abs(ocv_diff[ocv_diff < 0]))

        # 重新计算R0边界惩罚（用于详细输出）
        if not np.all((r0_points >= self.r0_bounds[0]) & (r0_points <= self.r0_bounds[1])):
            lower_violation = np.sum(np.maximum(self.r0_bounds[0] - r0_points, 0))
            upper_violation = np.sum(np.maximum(r0_points - self.r0_bounds[1], 0))
            r0_boundary_penalty = 100 * (lower_violation + upper_violation)

        # 总目标值 = MAE + OCV惩罚项 + R0惩罚项
        total_fitness = mae + ocv_penalty + r0_penalty

        # 详细打印评价指标（可选择性开启）
        if hasattr(self, 'verbose') and self.verbose:
            print(f"  评价指标详情:")
            print(f"    MAE (平均绝对误差): {mae:.6f} V")
            print(f"    RMSE (均方根误差): {rmse:.6f} V")
            print(f"    最大误差: {max_error:.6f} V")
            print(f"    OCV边界约束惩罚: {boundary_penalty:.6f}")
            print(f"    OCV单调性约束惩罚: {monotonic_penalty:.6f}")
            print(f"    R0边界约束惩罚: {r0_boundary_penalty:.6f}")
            print(f"    总OCV惩罚: {ocv_penalty:.6f}")
            print(f"    总R0惩罚: {r0_penalty:.6f}")
            print(f"    总适应度: {total_fitness:.6f}")
        else:
            # 简化输出
            print(f"MAE: {mae:.4f}V, OCV惩罚: {ocv_penalty:.4f}, R0惩罚: {r0_penalty:.4f}, 总适应度: {total_fitness:.4f}")

        return total_fitness
    
    def _plot_optimization_process(self):
        """绘制优化过程曲线"""
        if not self.optimization_history['iteration']:
            return
        
        plt.figure(figsize=(15, 5))
        
        # 1. 适应度变化曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.optimization_history['iteration'], 
                self.optimization_history['best_fitness'], 'b-')
        plt.xlabel('迭代次数')
        plt.ylabel('目标函数值')
        plt.title('优化过程收敛曲线')
        plt.grid(True)
        
        # 2. OCV曲线进化过程
        plt.subplot(1, 2, 2)
        for i in range(0, len(self.optimization_history['iteration']), 10):
            plt.plot(self.soc_points, self.optimization_history['best_ocv'][i], 
                    alpha=0.1, color='blue')
        plt.plot(self.soc_points, self.optimization_history['best_ocv'][-1], 
                'r-', linewidth=2, label='最终OCV曲线')
        plt.plot(self.soc_points, self.default_ocv, 'g--', label='默认OCV曲线')
        plt.fill_between(self.soc_points, 
                        self.ocv_bounds[0], 
                        self.ocv_bounds[1], 
                        color='gray', alpha=0.2, label='允许波动范围')
        plt.xlabel('SOC(%)')
        plt.ylabel('OCV(V)')
        plt.title('OCV曲线优化过程')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()

    def optimize_parameters(self):
        """运行参数优化"""
        print("开始电池参数优化...")
        print(f"GA参数: 种群大小={self.population_size}, 迭代代数={self.generations}")
        print(f"变异率={self.mutation_rate}, 交叉率={self.crossover_rate}, 精英比例={self.elite_ratio}")

        # 初始化种群
        population = self.toolbox.population(n=self.population_size)
        best_fitness_history = []

        for generation in range(self.generations):
            # 计算适应度
            fitnesses = [self.toolbox.evaluate(ind)[0] for ind in population]
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = (fit,)

            # 记录最佳适应度
            best_idx = np.argmin(fitnesses)
            best_fitness = fitnesses[best_idx]
            best_individual = population[best_idx]

            best_fitness_history.append(best_fitness)

            print(f"第{generation+1}代: 最佳适应度={best_fitness:.4f}")

            # 精英保留
            elite_individuals = tools.selBest(population, self.elite_size)

            # 选择用于繁殖的个体数量
            num_offspring = self.population_size - self.elite_size

            # 锦标赛选择
            selected = self.toolbox.select(population, num_offspring)

            # 交叉和变异生成新个体
            new_population = [self.toolbox.clone(ind) for ind in elite_individuals]

            for i in range(0, num_offspring, 2):
                if i < len(selected) - 1:
                    parent1 = selected[i]
                    parent2 = selected[i+1]
                else:
                    parent1 = selected[i]
                    parent2 = selected[0]

                child1, child2 = self.toolbox.mate(self.toolbox.clone(parent1), self.toolbox.clone(parent2))
                child1, = self.toolbox.mutate(child1)
                child2, = self.toolbox.mutate(child2)

                new_population.extend([child1, child2])

            # 确保种群大小
            population = new_population[:self.population_size]

        # 最终评估
        final_fitness = [self.toolbox.evaluate(ind)[0] for ind in population]
        best_idx = np.argmin(final_fitness)
        best_params = np.array(population[best_idx])
        best_mae = final_fitness[best_idx]

        print("\n优化完成!")

        # 解包最优参数
        initial_soc = best_params[0]
        ocv_points = best_params[1:len(self.soc_points)+1]
        r0_points = best_params[len(self.soc_points)+1:]

        # 计算详细的评价指标
        self.return_detailed_metrics = True
        final_metrics = self.simulate_battery(best_params)
        self.return_detailed_metrics = False

        mae_only = final_metrics['mae']
        rmse_final = final_metrics['rmse']
        max_error_final = final_metrics['max_error']

        # 计算约束违反情况
        boundary_violations = 0
        monotonic_violations = 0
        r0_boundary_violations = 0

        # OCV边界约束检查
        lower_violations = np.sum(np.maximum(self.ocv_bounds[0] - ocv_points, 0))
        upper_violations = np.sum(np.maximum(ocv_points - self.ocv_bounds[1], 0))
        boundary_violations = lower_violations + upper_violations

        # OCV单调性约束检查
        ocv_diff = np.diff(ocv_points)
        monotonic_violations = np.sum(ocv_diff < 0)

        # R0边界约束检查
        r0_lower_violations = np.sum(np.maximum(self.r0_bounds[0] - r0_points, 0))
        r0_upper_violations = np.sum(np.maximum(r0_points - self.r0_bounds[1], 0))
        r0_boundary_violations = r0_lower_violations + r0_upper_violations

        # 计算约束惩罚
        boundary_penalty = 100 * boundary_violations
        monotonic_penalty = 0.05 * np.sum(np.abs(ocv_diff[ocv_diff < 0]))
        r0_boundary_penalty = 100 * r0_boundary_violations
        total_penalty = boundary_penalty + monotonic_penalty + r0_boundary_penalty

        print("\n" + "="*60)
        print("详细评价指标报告")
        print("="*60)
        print(f"📊 主要性能指标:")
        print(f"   MAE (平均绝对误差): {mae_only:.6f} V")
        print(f"   RMSE (均方根误差): {rmse_final:.6f} V")
        print(f"   最大绝对误差: {max_error_final:.6f} V")
        print(f"   总适应度函数值: {best_mae:.6f}")

        print(f"\n🔒 约束满足情况:")
        print(f"   OCV边界约束违反: {boundary_violations:.6f} (惩罚: {boundary_penalty:.6f})")
        print(f"   OCV单调性约束违反: {monotonic_violations} 个点 (惩罚: {monotonic_penalty:.6f})")
        print(f"   R0边界约束违反: {r0_boundary_violations:.6f} (惩罚: {r0_boundary_penalty:.6f})")
        print(f"   总约束惩罚: {total_penalty:.6f}")

        print(f"\n⚙️  优化参数:")
        print(f"   最优初始SOC: {initial_soc:.2f}%")

        # 计算OCV统计信息
        ocv_deviations = ocv_points - self.default_ocv
        print(f"   OCV偏差统计:")
        print(f"     平均偏差: {np.mean(ocv_deviations):+.4f} V")
        print(f"     最大偏差: {np.max(np.abs(ocv_deviations)):+.4f} V")
        print(f"     标准差: {np.std(ocv_deviations):.4f} V")

        # 计算R0统计信息
        r0_deviations = r0_points - self.default_r0
        print(f"   R0偏差统计:")
        print(f"     平均偏差: {np.mean(r0_deviations):+.6f} Ω")
        print(f"     最大偏差: {np.max(np.abs(r0_deviations)):+.6f} Ω")
        print(f"     标准差: {np.std(r0_deviations):.6f} Ω")

        print(f"\n📈 性能评级:")
        if mae_only < 0.01:
            performance_level = "优秀 ⭐⭐⭐⭐⭐"
        elif mae_only < 0.03:
            performance_level = "良好 ⭐⭐⭐⭐"
        elif mae_only < 0.05:
            performance_level = "一般 ⭐⭐⭐"
        elif mae_only < 0.1:
            performance_level = "较差 ⭐⭐"
        else:
            performance_level = "很差 ⭐"

        print(f"   预测精度: {performance_level}")

        if total_penalty == 0:
            constraint_level = "完全满足 ✅"
        elif total_penalty < 0.1:
            constraint_level = "基本满足 ⚠️"
        else:
            constraint_level = "存在违反 ❌"

        print(f"   约束满足: {constraint_level}")
        print("="*60)

        print(f"\n📋 优化后的OCV曲线关键点:")
        for i in range(0, len(ocv_points), 5):  # 每5个点打印一个
            soc = self.soc_points[i]
            point = ocv_points[i]
            default = self.default_ocv[i]
            deviation = point - default
            print(f"   SOC {soc:3.0f}%: {point:.4f}V (默认:{default:.3f}V, 偏差:{deviation:+.3f}V)")

        print(f"\n📋 优化后的R0曲线关键点:")
        for i in range(len(r0_points)):  # 打印所有R0点
            soc = self.r0_soc_points[i]
            point = r0_points[i]
            default = self.default_r0[i]
            deviation = point - default
            print(f"   SOC {soc:3.0f}%: {point:.6f}Ω (默认:{default:.6f}Ω, 偏差:{deviation:+.6f}Ω)")

        # 绘制优化过程曲线
        self._plot_optimization_process()

        # 用最优参数运行模拟并绘图
        print("\n生成验证图...")
        self.simulate_battery(best_params, plot_comparison=True)

        return best_params, best_mae


# 使用示例
if __name__ == "__main__":
    # 加载数据
    data = pd.read_hdf("database/127_1_1_2023-12-25.h5")

    # 创建优化器实例
    optimizer = BatteryParameterOptimizer(
        data=data,
        current_col="2023-12-25_1#BMS-堆内-1簇组电流",
        voltage_col="2023-12-25_1#BMS-堆内-1簇单体电压1"
    )

    # 运行优化
    best_params, best_mae = optimizer.optimize_parameters()
