#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的OCV边界估算
"""

import os
import pandas as pd
import numpy as np

# 重新导入修改后的模块
import importlib
import data_driven_ocv_optimizer
importlib.reload(data_driven_ocv_optimizer)

from data_driven_ocv_optimizer import DataDrivenOCVOptimizer

def test_ocv_bounds():
    print('🚀 验证修改后的OCV边界估算')
    print('='*50)

    # 加载数据
    data = pd.read_hdf('database/127_1_1_2023-12-25.h5')

    # 查找列名
    current_col = None
    voltage_col = None  
    soc_col = None

    for col in data.columns:
        if '2023-12-25_1#BMS-堆内-1簇组电流' in col:
            current_col = col
        elif '2023-12-25_1#BMS-堆内-1簇单体电压1' in col:
            voltage_col = col
        elif '2023-12-25_1#BMS-堆内-1簇组SOC' in col:
            soc_col = col

    # 创建清理后的数据
    clean_data = pd.DataFrame({
        '电流': data[current_col],
        '电压': data[voltage_col],
        'SOC': data[soc_col]
    }, index=data.index)

    clean_data = clean_data.dropna()

    print(f'📊 数据概况:')
    print(f'  数据点数: {len(clean_data)}')
    
    voltage_min = clean_data['电压'].min()
    voltage_max = clean_data['电压'].max()
    soc_min = clean_data['SOC'].min()
    soc_max = clean_data['SOC'].max()
    
    print(f'  电压范围: {voltage_min:.3f}V ~ {voltage_max:.3f}V')
    print(f'  SOC范围: {soc_min:.1f}% ~ {soc_max:.1f}%')

    # 创建优化器
    print(f'\n🔧 创建修改后的优化器...')
    optimizer = DataDrivenOCVOptimizer(
        data=clean_data,
        current_col='电流',
        voltage_col='电压',
        soc_col='SOC',
        use_cuda=False
    )

    print(f'\n✅ 修改效果验证:')
    ocv_min, ocv_max = optimizer.ocv_bounds
    print(f'  修改后OCV边界: {ocv_min:.3f}V ~ {ocv_max:.3f}V')
    print(f'  边界范围: {ocv_max - ocv_min:.3f}V')
    print(f'  SOC采样点数: {len(optimizer.soc_points)}')
    print(f'  SOC覆盖范围: {optimizer.data_soc_range["coverage"]:.1f}%')

    # 验证边界合理性
    coverage_ok = (ocv_min <= voltage_min and ocv_max >= voltage_max)

    print(f'\n🎯 边界验证:')
    print(f'  下界覆盖: {"✅" if ocv_min <= voltage_min else "❌"} ({voltage_min - ocv_min:+.3f}V)')
    print(f'  上界覆盖: {"✅" if ocv_max >= voltage_max else "❌"} ({ocv_max - voltage_max:+.3f}V)')
    print(f'  整体评价: {"✅ 边界设置合理" if coverage_ok else "❌ 边界需要调整"}')

    # 对比旧方法
    voltage_data = clean_data['电压'].values
    v_min_5 = np.percentile(voltage_data, 5)
    v_max_95 = np.percentile(voltage_data, 95)
    old_ocv_min = max(2.5, v_min_5 - 0.1)
    old_ocv_max = min(4.0, v_max_95 + 0.2)

    print(f'\n📈 新旧方法对比:')
    print(f'  旧方法边界: {old_ocv_min:.3f}V ~ {old_ocv_max:.3f}V (范围: {old_ocv_max - old_ocv_min:.3f}V)')
    print(f'  新方法边界: {ocv_min:.3f}V ~ {ocv_max:.3f}V (范围: {ocv_max - ocv_min:.3f}V)')
    print(f'  实际数据范围: {voltage_min:.3f}V ~ {voltage_max:.3f}V (范围: {voltage_max - voltage_min:.3f}V)')

    # 改进效果
    old_range = old_ocv_max - old_ocv_min
    new_range = ocv_max - ocv_min
    print(f'\n🎯 改进效果:')
    print(f'  搜索空间缩减: {((old_range - new_range) / old_range * 100):.1f}%')
    print(f'  精度提升: {(old_range / new_range):.1f}倍')
    print(f'  上界优化: {old_ocv_max - ocv_max:.3f}V')
    print(f'  下界优化: {ocv_min - old_ocv_min:.3f}V')

    return optimizer

if __name__ == "__main__":
    optimizer = test_ocv_bounds()
